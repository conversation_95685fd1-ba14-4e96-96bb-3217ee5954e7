//! # Universal Interner Demo
//!
//! This example demonstrates TAMTIL's new universal interner with memory-mapped 1GB files.
//! 
//! Key features demonstrated:
//! - Universal deduplication across all actors
//! - Memory-mapped files for efficient I/O
//! - Symbol-based storage (actors store only symbols, not data)
//! - Zero-copy access via rkyv
//! - Persistence guarantee (when remember() returns, data is securely persisted)

use tamtil::*;
use tamtil::todoapp::*;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize tracing
    tracing_subscriber::fmt::init();

    println!("🚀 TAMTIL Universal Interner Demo");
    println!("==================================\n");

    // Create platform with custom disk path
    let platform = Platform::with_disk_path("./demo_interner_data");

    // Create multiple actors that will share the same interner
    let actor1_id = ActorId::new("demo/actor1");
    let actor2_id = ActorId::new("demo/actor2");
    let actor3_id = ActorId::new("demo/actor3");

    let todo_list1 = TodoList::new(actor1_id.clone());
    let todo_list2 = TodoList::new(actor2_id.clone());
    let todo_list3 = TodoList::new(actor3_id.clone());

    let handle1 = platform.spawn(actor1_id.clone(), todo_list1).await?;
    let handle2 = platform.spawn(actor2_id.clone(), todo_list2).await?;
    let handle3 = platform.spawn(actor3_id.clone(), todo_list3).await?;

    // Start all actors
    println!("📋 Starting actors...");
    handle1.act(TodoAction::Start).await?;
    handle2.act(TodoAction::Start).await?;
    handle3.act(TodoAction::Start).await?;

    // Create identical todos in different actors (should be deduplicated)
    println!("\n🔄 Creating identical todos (demonstrating deduplication)...");
    
    let identical_todo_action = TodoAction::Create {
        title: "Identical Task".to_string(),
        description: "This exact same todo will be created in all actors".to_string(),
        priority: Priority::Medium,
    };

    // Create the same todo in all three actors
    let reaction1 = handle1.act(identical_todo_action.clone()).await?;
    let reaction2 = handle2.act(identical_todo_action.clone()).await?;
    let reaction3 = handle3.act(identical_todo_action.clone()).await?;

    if let (TodoReaction::Created { todo: todo1 }, 
            TodoReaction::Created { todo: todo2 }, 
            TodoReaction::Created { todo: todo3 }) = (reaction1, reaction2, reaction3) {
        println!("✅ Actor1 created todo: {} (ID: {})", todo1.title, todo1.id);
        println!("✅ Actor2 created todo: {} (ID: {})", todo2.title, todo2.id);
        println!("✅ Actor3 created todo: {} (ID: {})", todo3.title, todo3.id);
    }

    // Create unique todos to show mixed deduplication
    println!("\n🎯 Creating unique todos...");
    
    handle1.act(TodoAction::Create {
        title: "Actor1 Unique Task".to_string(),
        description: "Only in actor1".to_string(),
        priority: Priority::High,
    }).await?;

    handle2.act(TodoAction::Create {
        title: "Actor2 Unique Task".to_string(),
        description: "Only in actor2".to_string(),
        priority: Priority::Low,
    }).await?;

    // Wait for persistence
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

    // Demonstrate universal interner statistics
    println!("\n📊 Universal Interner Statistics:");
    let disk = Disk::new("./demo_interner_data");

    for (i, actor_id) in [&actor1_id, &actor2_id, &actor3_id].iter().enumerate() {
        let keys = disk.keys(actor_id).await?;
        let stats = disk.stats(actor_id).await?;
        
        println!("  Actor{} ({}): {} keys, {} entries", 
                 i + 1, actor_id.as_str(), keys.len(), stats.entry_count);
        
        // Show some keys
        if !keys.is_empty() {
            println!("    Sample keys: {:?}", &keys[..keys.len().min(3)]);
        }
    }

    // Check universal interner file
    let interner_path = std::path::Path::new("./demo_interner_data/universal_interner.tamtil");
    if interner_path.exists() {
        let metadata = std::fs::metadata(interner_path)?;
        println!("\n💾 Universal interner file: {} bytes", metadata.len());
        println!("   Location: {}", interner_path.display());
    }

    // Demonstrate data retrieval (zero-copy via symbols)
    println!("\n🔍 Retrieving data via universal interner...");
    
    if let Some(title_value) = disk.get(&actor1_id, "todo:1:title").await? {
        println!("✅ Retrieved from Actor1: {}", title_value);
    }
    
    if let Some(title_value) = disk.get(&actor2_id, "todo:1:title").await? {
        println!("✅ Retrieved from Actor2: {}", title_value);
    }

    // Show deduplication benefits
    println!("\n🎉 Deduplication Benefits:");
    println!("   - Identical data stored only once in universal interner");
    println!("   - Actors store only symbols (u64 IDs) in memory");
    println!("   - Zero-copy access via rkyv memory-mapped regions");
    println!("   - Automatic garbage collection (future feature)");
    println!("   - 1GB memory-mapped files for maximum performance");

    // Clean up
    println!("\n🧹 Cleaning up demo data...");
    let _ = std::fs::remove_dir_all("./demo_interner_data");

    println!("\n✨ Universal Interner Demo Complete!");
    println!("   The new disk system provides:");
    println!("   ✓ Universal deduplication across all actors");
    println!("   ✓ Memory-mapped 1GB files for performance");
    println!("   ✓ Symbol-based storage (actors keep only u64 IDs)");
    println!("   ✓ Zero-copy rkyv access");
    println!("   ✓ Persistence guarantee on remember() return");

    Ok(())
}
