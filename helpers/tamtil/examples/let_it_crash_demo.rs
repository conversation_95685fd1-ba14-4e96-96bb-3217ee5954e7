//! # "Let It Crash" Pattern Demo
//!
//! This example demonstrates TAMTIL's "let it crash" pattern where platform and context
//! actors can crash and restart seamlessly using the universal interner for state recovery.
//! 
//! Key features demonstrated:
//! - Platform and context actors follow action→reaction pattern
//! - State persistence via remember() to universal interner
//! - Automatic state recovery via recall() on restart
//! - Seamless crash recovery following "let it crash" philosophy
//! - Hierarchical actor state restoration

use tamtil::*;
use tamtil::todoapp::*;
use tamtil::platform;
use tamtil::context;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize tracing
    tracing_subscriber::fmt::init();

    println!("💥 TAMTIL 'Let It Crash' Pattern Demo");
    println!("=====================================\n");

    // Create platform with persistent storage
    let platform = Platform::with_disk_path("./crash_recovery_data");

    // === PHASE 1: Initial Setup ===
    println!("🚀 Phase 1: Setting up platform and context actors...");

    // Create platform actor
    let platform_id = ActorId::new("demo.tamtil.com");
    let platform_actor = platform::Actor::new(platform_id.clone());
    let platform_handle = platform.spawn(platform_id.clone(), platform_actor).await?;

    // Start platform
    let reaction = platform_handle.act(platform::Action::Standard(StandardAction::Start)).await?;
    println!("✅ Platform started: {:?}", reaction);

    // Create context actor
    let context_id = ActorId::new("demo.tamtil.com/web");
    let context_actor = context::Actor::new(context_id.clone());
    let context_handle = platform.spawn(context_id.clone(), context_actor).await?;

    // Start context
    let reaction = context_handle.act(context::Action::Standard(StandardAction::Start)).await?;
    println!("✅ Context started: {:?}", reaction);

    // Start some contexts via platform
    let reaction = platform_handle.act(platform::Action::StartContext { 
        name: "users".to_string() 
    }).await?;
    println!("✅ Platform started context: {:?}", reaction);

    let reaction = platform_handle.act(platform::Action::StartContext { 
        name: "orders".to_string() 
    }).await?;
    println!("✅ Platform started context: {:?}", reaction);

    // Start some actors via context
    let reaction = context_handle.act(context::Action::StartActor { 
        name: "todos".to_string() 
    }).await?;
    println!("✅ Context started actor: {:?}", reaction);

    let reaction = context_handle.act(context::Action::StartActor { 
        name: "analytics".to_string() 
    }).await?;
    println!("✅ Context started actor: {:?}", reaction);

    // Wait for persistence
    tokio::time::sleep(tokio::time::Duration::from_millis(200)).await;

    // === PHASE 2: Simulate Crash ===
    println!("\n💥 Phase 2: Simulating crash (stopping actors)...");

    // Stop actors (simulating crash)
    platform_handle.shutdown().await?;
    context_handle.shutdown().await?;
    println!("✅ Actors stopped (crash simulated)");

    // Wait a bit to simulate downtime
    tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;

    // === PHASE 3: Recovery ===
    println!("\n🔄 Phase 3: Recovery - restarting with state restoration...");

    // Create new platform actor (simulating restart)
    let new_platform_actor = platform::Actor::new(platform_id.clone());
    let new_platform_handle = platform.spawn(platform_id.clone(), new_platform_actor).await?;

    // Start platform - should automatically recover state
    let reaction = new_platform_handle.act(platform::Action::Standard(StandardAction::Start)).await?;
    match reaction {
        platform::Reaction::StateRecovered { contexts } => {
            println!("🎉 Platform recovered {} contexts: {:?}", contexts.len(), contexts);
        }
        platform::Reaction::Standard(StandardReaction::Started) => {
            println!("✅ Platform started fresh (no previous state found)");
        }
        _ => println!("⚠️  Unexpected platform reaction: {:?}", reaction),
    }

    // Create new context actor (simulating restart)
    let new_context_actor = context::Actor::new(context_id.clone());
    let new_context_handle = platform.spawn(context_id.clone(), new_context_actor).await?;

    // Start context - should automatically recover state
    let reaction = new_context_handle.act(context::Action::Standard(StandardAction::Start)).await?;
    match reaction {
        context::Reaction::StateRecovered { actors } => {
            println!("🎉 Context recovered {} actors: {:?}", actors.len(), actors);
        }
        context::Reaction::Standard(StandardReaction::Started) => {
            println!("✅ Context started fresh (no previous state found)");
        }
        _ => println!("⚠️  Unexpected context reaction: {:?}", reaction),
    }

    // === PHASE 4: Explicit Recovery ===
    println!("\n🔧 Phase 4: Testing explicit recovery actions...");

    // Test explicit platform recovery
    let reaction = new_platform_handle.act(platform::Action::RecoverState).await?;
    if let platform::Reaction::StateRecovered { contexts } = reaction {
        println!("✅ Explicit platform recovery: {} contexts", contexts.len());
    }

    // Test explicit context recovery
    let reaction = new_context_handle.act(context::Action::RecoverState).await?;
    if let context::Reaction::StateRecovered { actors } = reaction {
        println!("✅ Explicit context recovery: {} actors", actors.len());
    }

    // === PHASE 5: Verification ===
    println!("\n🔍 Phase 5: Verifying universal interner state...");

    let disk = Disk::new("./crash_recovery_data");

    // Check platform state
    let platform_keys = disk.keys(&platform_id).await?;
    println!("📊 Platform keys in interner: {} keys", platform_keys.len());
    for key in platform_keys.iter().take(5) {
        if let Some(value) = disk.get(&platform_id, key).await? {
            println!("   {}: {}", key, value);
        }
    }

    // Check context state
    let context_keys = disk.keys(&context_id).await?;
    println!("📊 Context keys in interner: {} keys", context_keys.len());
    for key in context_keys.iter().take(5) {
        if let Some(value) = disk.get(&context_id, key).await? {
            println!("   {}: {}", key, value);
        }
    }

    // Check universal interner file
    let interner_path = std::path::Path::new("./crash_recovery_data/universal_interner.tamtil");
    if interner_path.exists() {
        let metadata = std::fs::metadata(interner_path)?;
        println!("💾 Universal interner: {} bytes", metadata.len());
    }

    // === PHASE 6: Cleanup ===
    println!("\n🧹 Phase 6: Cleanup...");

    // Graceful shutdown
    new_platform_handle.shutdown().await?;
    new_context_handle.shutdown().await?;

    // Clean up demo data
    let _ = std::fs::remove_dir_all("./crash_recovery_data");

    println!("\n✨ 'Let It Crash' Demo Complete!");
    println!("   Key achievements:");
    println!("   ✓ Platform and context actors follow action→reaction pattern");
    println!("   ✓ State automatically persisted via remember() to universal interner");
    println!("   ✓ Seamless crash recovery via recall() on restart");
    println!("   ✓ 'Let it crash' philosophy - actors restart and continue seamlessly");
    println!("   ✓ Hierarchical state restoration maintains actor relationships");

    Ok(())
}
