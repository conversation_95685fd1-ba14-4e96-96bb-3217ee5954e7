//! # TAMTIL - The Actor Model That Is Like
//!
//! A high-performance actor system with rkyv zero-copy serialization, designed like an opera
//! where components work independently but in harmony.
//!
//! ## Table of Contents
//!
//! 1. [Core Concepts](#core-concepts)
//! 2. [Architecture](#architecture)
//! 3. [Addressing System](#addressing-system)
//! 4. [Developer API](#developer-api)
//! 5. [Platform Module](#platform-module)
//! 6. [Context Module](#context-module)
//! 7. [Actor Module](#actor-module)
//! 8. [Infrastructure](#infrastructure)
//! 9. [Complete Example](#complete-example)
//! 10. [Tests](#tests)
//!
//! ## Core Concepts
//!
//! Everything is an actor following the universal pattern: `actor.act(action, actors)`
//!
//! ### Key Features
//! - **Alice <PERSON>'s actor pattern** with tokio hidden behind platform abstraction
//! - **rkyv zero-copy serialization** for maximum performance
//! - **Single-word naming** convention throughout (unless impossible)
//! - **Hierarchical actor spawning** via `actors.spawn(name)`
//! - **URL-based addressing** with full hierarchy support
//! - **Action→reaction pattern** for all interactions
//! - **No locking** - actors handle concurrency elegantly
//! - **Automatic child cleanup** when parent stops
//!
//! ## Architecture
//!
//! ```text
//! Platform (platform.com)
//! └── Context (platform.com/web)
//!     └── Context Instance (platform.com/web/main)
//!         └── Actor (platform.com/web/main/counter)
//!             └── Actor Instance (platform.com/web/main/counter/stats)
//!                 └── Child Actor (platform.com/web/main/counter/stats/session)
//!                     └── Child Instance (platform.com/web/main/counter/stats/session/user123)
//! ```
//!
//! ## Addressing System
//!
//! TAMTIL supports hierarchical URL-based addressing up to 6+ levels:
//! - `platform.com`
//! - `platform.com/context_name`
//! - `platform.com/context_name/context_id`
//! - `platform.com/context_name/context_id/actor_name`
//! - `platform.com/context_name/context_id/actor_name/actor_id`
//! - `platform.com/context_name/context_id/actor_name/actor_id/child_name`
//! - `platform.com/context_name/context_id/actor_name/actor_id/child_name/child_id`
//! - And so on...
//!
//! ## Developer API
//!
//! Primary interactions:
//! - `actors.spawn(name)` - spawn child actor
//! - `actors.actor(id).act(action)` - send action to actor
//! - All actors receive `actors` parameter for spawning and communication

use tokio::sync::{mpsc, oneshot, RwLock};
use async_trait::async_trait;
use thiserror::Error;
use rkyv::{Archive, Serialize, Deserialize, rancor::Error as RancorError};
use std::collections::HashMap;
use std::fmt;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use std::sync::atomic::{AtomicU64, Ordering};
use memmap2::{MmapMut, MmapOptions};

// Network imports for distributed communication (future use)
use std::net::SocketAddr;

// ============================================================================
// CHAPTER 1: CORE TYPES AND TRAITS
// ============================================================================

/// Core error types for TAMTIL
#[derive(Error, Debug)]
pub enum TamtilError {
    #[error("Actor not found: {id}")]
    ActorNotFound { id: String },
    #[error("Platform error: {message}")]
    Platform { message: String },
    #[error("Communication error: {message}")]
    Communication { message: String },
    #[error("Serialization error: {message}")]
    Serialization { message: String },
    #[error("Invalid address format: {address}")]
    InvalidAddress { address: String },
}

/// Result type for TAMTIL operations
pub type TamtilResult<T> = Result<T, TamtilError>;

// ============================================================================
// ACTOR REGISTRY - Global Actor Management and Message Routing
// ============================================================================

/// Legacy message envelope for routing between actors (kept for compatibility)
#[derive(Debug)]
pub struct LegacyActorMessage {
    pub from: ActorId,
    pub to: ActorId,
    pub payload: Vec<u8>, // Serialized action
    pub respond_to: Option<oneshot::Sender<Vec<u8>>>, // Serialized reaction
}

/// Legacy actor handle for message routing (kept for compatibility)
pub trait LegacyActorHandle: Send + Sync {
    fn send_message(&self, message: LegacyActorMessage) -> TamtilResult<()>;
    fn actor_id(&self) -> &ActorId;
}

/// Global actor registry for message routing
#[derive(Clone)]
pub struct ActorRegistry {
    actors: Arc<RwLock<HashMap<ActorId, Arc<dyn LegacyActorHandle>>>>,
    next_id: Arc<AtomicU64>,
}

impl ActorRegistry {
    /// Create new actor registry
    pub fn new() -> Self {
        Self {
            actors: Arc::new(RwLock::new(HashMap::new())),
            next_id: Arc::new(AtomicU64::new(1)),
        }
    }

    /// Register an actor
    pub async fn register(&self, id: ActorId, handle: Arc<dyn LegacyActorHandle>) {
        let mut actors = self.actors.write().await;
        actors.insert(id, handle);
    }

    /// Unregister an actor
    pub async fn unregister(&self, id: &ActorId) {
        let mut actors = self.actors.write().await;
        actors.remove(id);
    }

    /// Route message to actor
    pub async fn route_message(&self, message: LegacyActorMessage) -> TamtilResult<()> {
        let actors = self.actors.read().await;
        if let Some(handle) = actors.get(&message.to) {
            handle.send_message(message)?;
            Ok(())
        } else {
            Err(TamtilError::ActorNotFound {
                id: message.to.as_str().to_string()
            })
        }
    }

    /// Get next unique ID
    pub fn next_id(&self) -> u64 {
        self.next_id.fetch_add(1, Ordering::SeqCst)
    }

    /// Check if actor exists
    pub async fn exists(&self, id: &ActorId) -> bool {
        let actors = self.actors.read().await;
        actors.contains_key(id)
    }

    /// List all registered actors
    pub async fn list_actors(&self) -> Vec<ActorId> {
        let actors = self.actors.read().await;
        actors.keys().cloned().collect()
    }
}

impl Default for ActorRegistry {
    fn default() -> Self {
        Self::new()
    }
}

// ============================================================================
// MEMORY SYSTEM - Graph-based Actor State Management
// ============================================================================

/// Memory value that can store basic serializable data
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum MemoryValue {
    String(String),
    Number(f64),
    Boolean(bool),
    Bytes(Vec<u8>),
}

impl MemoryValue {
    /// Create a string value
    pub fn string(s: impl Into<String>) -> Self {
        Self::String(s.into())
    }

    /// Create a number value
    pub fn number(n: f64) -> Self {
        Self::Number(n)
    }

    /// Create a boolean value
    pub fn boolean(b: bool) -> Self {
        Self::Boolean(b)
    }

    /// Create a bytes value
    pub fn bytes(b: Vec<u8>) -> Self {
        Self::Bytes(b)
    }
}

impl fmt::Display for MemoryValue {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            MemoryValue::String(s) => write!(f, "{}", s),
            MemoryValue::Number(n) => write!(f, "{}", n),
            MemoryValue::Boolean(b) => write!(f, "{}", b),
            MemoryValue::Bytes(b) => write!(f, "{:?}", b),
        }
    }
}

/// Memory operations that reactions can perform
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum MemoryOperation {
    /// Create a new memory node
    Create { key: String, value: MemoryValue },
    /// Update an existing memory node
    Update { key: String, value: MemoryValue },
    /// Delete a memory node
    Delete { key: String },
    /// Create a relationship between two memory nodes
    Link { from: String, to: String, relation: String },
    /// Remove a relationship between two memory nodes
    Unlink { from: String, to: String, relation: String },
}

/// A memory node in the actor's memory graph
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub struct MemoryNode {
    pub key: String,
    pub value: MemoryValue,
    pub links: Vec<(String, Vec<String>)>, // relation -> [target_keys] (using Vec for rkyv)
    pub created: u64,
    pub updated: u64,
}

/// Memory graph for an actor - stores all state as a graph of nodes
#[derive(Debug, Clone, Default)]
pub struct MemoryGraph {
    nodes: HashMap<String, MemoryNode>,
    reactions: Vec<(u64, Vec<u8>)>, // timestamp -> serialized reaction (source of truth)
}

impl MemoryGraph {
    /// Create new empty memory graph
    pub fn new() -> Self {
        Self {
            nodes: HashMap::new(),
            reactions: Vec::new(),
        }
    }

    /// Apply memory operations to the graph
    pub fn apply(&mut self, operations: Vec<MemoryOperation>) -> TamtilResult<()> {
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        for operation in operations {
            match operation {
                MemoryOperation::Create { key, value } => {
                    let node = MemoryNode {
                        key: key.clone(),
                        value,
                        links: Vec::new(),
                        created: timestamp,
                        updated: timestamp,
                    };
                    self.nodes.insert(key, node);
                }

                MemoryOperation::Update { key, value } => {
                    if let Some(node) = self.nodes.get_mut(&key) {
                        node.value = value;
                        node.updated = timestamp;
                    }
                }

                MemoryOperation::Delete { key } => {
                    self.nodes.remove(&key);
                    // Remove all links to this node
                    for node in self.nodes.values_mut() {
                        for (_, targets) in node.links.iter_mut() {
                            targets.retain(|target| target != &key);
                        }
                    }
                }

                MemoryOperation::Link { from, to, relation } => {
                    if let Some(node) = self.nodes.get_mut(&from) {
                        // Find existing relation or create new one
                        if let Some((_, targets)) = node.links.iter_mut().find(|(rel, _)| rel == &relation) {
                            targets.push(to);
                        } else {
                            node.links.push((relation, vec![to]));
                        }
                    }
                }

                MemoryOperation::Unlink { from, to, relation } => {
                    if let Some(node) = self.nodes.get_mut(&from) {
                        if let Some((_, targets)) = node.links.iter_mut().find(|(rel, _)| rel == &relation) {
                            targets.retain(|target| target != &to);
                        }
                    }
                }
            }
        }

        Ok(())
    }

    /// Get a memory node by key
    pub fn get(&self, key: &str) -> Option<&MemoryNode> {
        self.nodes.get(key)
    }

    /// Get all nodes
    pub fn nodes(&self) -> &HashMap<String, MemoryNode> {
        &self.nodes
    }

    /// Query nodes by value pattern (simplified JSON path-like queries)
    pub fn query(&self, pattern: &str) -> Vec<&MemoryNode> {
        // Simple implementation - can be enhanced with proper JSON path queries
        self.nodes.values()
            .filter(|node| {
                node.value.to_string().contains(pattern) ||
                node.key.contains(pattern)
            })
            .collect()
    }

    /// Store a reaction as source of truth
    pub fn remember(&mut self, reaction_bytes: Vec<u8>) {
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        self.reactions.push((timestamp, reaction_bytes));
    }

    /// Get all stored reactions (source of truth)
    pub fn reactions(&self) -> &[(u64, Vec<u8>)] {
        &self.reactions
    }
}

/// Memories registry for managing actor state through reactions
///
/// Provides the developer API for:
/// - Remembering reactions via `memories.remember(reaction)`
/// - Querying memories via `memories.recall(query)`
/// - Managing actor state as a graph of memory nodes
#[derive(Clone)]
pub struct Memories {
    actor_id: ActorId,
    disk: Disk,
}

impl Memories {
    /// Create new memories registry for an actor
    pub fn new(actor_id: ActorId, disk: Disk) -> Self {
        Self { actor_id, disk }
    }

    /// Remember a reaction - this is the only way to change actor state
    ///
    /// The reaction's remember() method returns memory operations that are applied
    /// to the actor's memory graph. Reactions are stored as the source of truth.
    pub async fn remember<R>(&self, reaction: R) -> TamtilResult<()>
    where
        R: Reaction,
    {
        // Serialize the reaction using rkyv for storage as source of truth
        let _reaction_bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&reaction)
            .map_err(|e| TamtilError::Serialization {
                message: format!("Failed to serialize reaction: {}", e)
            })?;

        // Get memory operations from the reaction
        let operations = reaction.remember().await?;

        // Apply memory operations to disk storage
        let operation_count = operations.len();
        if !operations.is_empty() {
            self.disk.apply(&self.actor_id, operations).await?;
            tracing::debug!("Actor {} persisted {} memory operations to disk",
                           self.actor_id.as_str(), operation_count);
        }

        Ok(())
    }

    /// Recall memories using a query pattern
    ///
    /// Unified API for querying both raw memories and visual memories
    pub async fn recall(&self, query: &str) -> TamtilResult<Vec<MemoryValue>> {
        let keys = self.disk.keys(&self.actor_id).await?;
        let mut results = Vec::new();

        for key in keys {
            if key.contains(query) {
                if let Some(value) = self.disk.get(&self.actor_id, &key).await? {
                    results.push(value);
                }
            }
        }

        tracing::debug!("Actor {} recalled {} memories matching query: {}",
                       self.actor_id.as_str(), results.len(), query);

        Ok(results)
    }

    /// Get specific value by key
    pub async fn get(&self, key: &str) -> TamtilResult<Option<MemoryValue>> {
        self.disk.get(&self.actor_id, key).await
    }

    /// List all keys
    pub async fn keys(&self) -> TamtilResult<Vec<String>> {
        self.disk.keys(&self.actor_id).await
    }

    /// Get storage statistics
    pub async fn stats(&self) -> TamtilResult<DiskStats> {
        self.disk.stats(&self.actor_id).await
    }

    /// Get actor ID
    pub fn actor_id(&self) -> &ActorId {
        &self.actor_id
    }
}

/// Trait for reactions that can be remembered
///
/// Reactions implement this trait to define what memory operations they produce
/// when remembered. This is how actor state changes are driven by reactions.
#[async_trait]
pub trait Reaction: Archive + for<'a> Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, RancorError>> + Send + Sync + 'static {
    /// Generate memory operations when this reaction is remembered
    ///
    /// This method defines how the reaction changes the actor's memory state.
    /// The operations returned will be applied to the actor's memory graph.
    async fn remember(&self) -> TamtilResult<Vec<MemoryOperation>>;
}

// ============================================================================
// DISTRIBUTED INTERNER - OmniPaxos-based Universal Interner with Fault Tolerance
// ============================================================================

/// Distributed interner operation for replication across shards
///
/// These operations represent changes to the universal interner that need to be
/// replicated across context shards for fault tolerance. All data is already
/// validated and serialized with rkyv before reaching this level.
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum InternerOperation {
    /// Store rkyv-serialized data in the universal interner
    Store {
        /// Hash of the data for deduplication
        hash: u64,
        /// The rkyv-serialized data (already validated)
        data: Vec<u8>,
        /// Timestamp when stored
        timestamp: u64,
        /// Context shard that initiated the store
        shard_id: u64,
    },
    /// Update actor symbol mapping within a shard
    MapSymbol {
        /// Actor ID that owns this mapping
        actor_id: ActorId,
        /// Key within the actor's namespace
        key: String,
        /// Symbol pointing to interned data
        symbol: Symbol,
        /// Timestamp of mapping
        timestamp: u64,
        /// Context shard this mapping belongs to
        shard_id: u64,
    },
    /// Remove actor symbol mapping
    UnmapSymbol {
        /// Actor ID that owns this mapping
        actor_id: ActorId,
        /// Key to remove from actor's namespace
        key: String,
        /// Timestamp of removal
        timestamp: u64,
        /// Context shard this mapping belongs to
        shard_id: u64,
    },
}

/// Node configuration for distributed interner
#[derive(Debug, Clone)]
pub struct NodeConfig {
    /// Unique node ID in the cluster
    pub node_id: u64,
    /// Network address for this node
    pub address: SocketAddr,
    /// Platform ID this node belongs to
    pub platform_id: ActorId,
}

/// Distributed interner shard leveraging platform/context hierarchy
///
/// Each context forms its own shard for fault tolerance. The platform orchestrates
/// multiple context shards, enabling natural distribution with rkyv zero-copy.
#[derive(Clone)]
pub struct DistributedShard {
    /// Base path for storage files, organized by platform/context/shard
    base_path: PathBuf,
    /// Node ID for this shard instance
    node_id: u64,
    /// Platform ID for big orchestrator level
    platform_id: ActorId,
    /// Context ID for shard identification
    context_id: ActorId,
    /// Shard ID within the context
    shard_id: u64,
    /// Operation log for replication (rkyv serialized)
    operation_log: Arc<RwLock<Vec<InternerOperation>>>,
    /// Peer nodes in this shard
    peer_nodes: Arc<RwLock<Vec<NodeConfig>>>,
}

impl DistributedShard {
    /// Create new distributed shard for a context
    pub fn new(
        base_path: impl AsRef<Path>,
        node_id: u64,
        platform_id: ActorId,
        context_id: ActorId,
        shard_id: u64,
        peer_nodes: Vec<NodeConfig>,
    ) -> Self {
        Self {
            base_path: base_path.as_ref().to_path_buf(),
            node_id,
            platform_id,
            context_id,
            shard_id,
            operation_log: Arc::new(RwLock::new(Vec::new())),
            peer_nodes: Arc::new(RwLock::new(peer_nodes)),
        }
    }

    /// Get storage path organized by platform/context/shard hierarchy
    fn storage_path(&self) -> PathBuf {
        self.base_path
            .join("distributed_interner")
            .join(self.platform_id.as_str())
            .join(self.context_id.as_str())
            .join(format!("shard_{}", self.shard_id))
            .join(format!("node_{}", self.node_id))
    }

    /// Initialize storage directory
    async fn ensure_storage_dir(&self) -> TamtilResult<()> {
        let path = self.storage_path();
        tokio::fs::create_dir_all(&path).await
            .map_err(|e| TamtilError::Platform {
                message: format!("Failed to create shard storage directory: {}", e)
            })?;
        Ok(())
    }

    /// Initialize distributed shard
    pub async fn initialize(&self) -> TamtilResult<()> {
        self.ensure_storage_dir().await?;

        tracing::info!("Distributed shard initialized for platform {} context {} shard {} node {}",
                      self.platform_id.as_str(), self.context_id.as_str(), self.shard_id, self.node_id);
        Ok(())
    }

    /// Apply interner operation to this shard (with future replication)
    pub async fn apply_operation(&self, operation: InternerOperation) -> TamtilResult<()> {
        let mut log = self.operation_log.write().await;
        log.push(operation);

        // TODO: In a full implementation, replicate to peer nodes
        // For now, just log locally

        Ok(())
    }

    /// Get shard identifier for routing
    pub fn shard_key(&self) -> String {
        format!("{}/shard_{}", self.context_id.as_str(), self.shard_id)
    }
}

/// Context shard configuration for distributed fault-tolerant clusters
#[derive(Debug, Clone)]
pub struct ContextShardConfig {
    /// Context ID that this shard belongs to
    pub context_id: ActorId,
    /// Shard ID within the context
    pub shard_id: u64,
    /// Nodes participating in this context shard
    pub nodes: Vec<NodeConfig>,
    /// Platform orchestrator this shard reports to
    pub platform_id: ActorId,
}

impl ContextShardConfig {
    /// Create context shard config
    pub fn new(
        context_id: ActorId,
        shard_id: u64,
        platform_id: ActorId,
        nodes: Vec<NodeConfig>,
    ) -> Self {
        Self {
            context_id,
            shard_id,
            nodes,
            platform_id,
        }
    }

    /// Get shard identifier for routing
    pub fn shard_key(&self) -> String {
        format!("{}/shard_{}", self.context_id.as_str(), self.shard_id)
    }
}

/// Context shard configuration for distributed fault-tolerant clusters
#[derive(Debug, Clone)]
pub struct ContextShardConfig {
    /// Context ID that this shard belongs to
    pub context_id: ActorId,
    /// Shard ID within the context
    pub shard_id: u64,
    /// Nodes participating in this context shard
    pub nodes: Vec<NodeConfig>,
    /// Minimum nodes required for consensus in this shard
    pub quorum_size: usize,
    /// Platform orchestrator this shard reports to
    pub platform_id: ActorId,
}

impl ContextShardConfig {
    /// Create context shard config
    pub fn new(
        context_id: ActorId,
        shard_id: u64,
        platform_id: ActorId,
        nodes: Vec<NodeConfig>,
    ) -> Self {
        let quorum_size = (nodes.len() / 2) + 1;
        Self {
            context_id,
            shard_id,
            nodes,
            quorum_size,
            platform_id,
        }
    }

    /// Convert to OmniPaxos ClusterConfig for this shard
    pub fn to_omnipaxos_config(&self) -> ClusterConfig {
        let node_ids: Vec<u64> = self.nodes.iter().map(|n| n.node_id).collect();
        ClusterConfig {
            configuration_id: self.shard_id,
            nodes: node_ids,
            flexible_quorum: None,
        }
    }

    /// Get shard identifier for routing
    pub fn shard_key(&self) -> String {
        format!("{}/shard_{}", self.context_id.as_str(), self.shard_id)
    }
}

/// Platform orchestrator configuration managing multiple context shards
#[derive(Debug, Clone)]
pub struct PlatformShardConfig {
    /// Platform ID for the big orchestrator
    pub platform_id: ActorId,
    /// All context shards managed by this platform
    pub context_shards: Vec<ContextShardConfig>,
    /// Consistent hashing ring for actor placement
    pub hash_ring: Vec<u64>, // Shard IDs in hash order
}

impl PlatformShardConfig {
    /// Create platform shard configuration
    pub fn new(platform_id: ActorId, context_shards: Vec<ContextShardConfig>) -> Self {
        // Build hash ring for consistent hashing
        let mut hash_ring: Vec<u64> = context_shards.iter().map(|s| s.shard_id).collect();
        hash_ring.sort();

        Self {
            platform_id,
            context_shards,
            hash_ring,
        }
    }

    /// Determine which context shard an actor should be placed on
    pub fn route_actor(&self, actor_id: &ActorId) -> Option<&ContextShardConfig> {
        if self.hash_ring.is_empty() {
            return None;
        }

        // Use consistent hashing based on actor ID
        let hash = self.hash_actor_id(actor_id);
        let shard_id = self.find_shard_for_hash(hash);

        self.context_shards.iter().find(|s| s.shard_id == shard_id)
    }

    /// Hash actor ID for consistent placement
    fn hash_actor_id(&self, actor_id: &ActorId) -> u64 {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        actor_id.as_str().hash(&mut hasher);
        hasher.finish()
    }

    /// Find appropriate shard for hash using consistent hashing
    fn find_shard_for_hash(&self, hash: u64) -> u64 {
        // Find first shard ID >= hash, or wrap to first shard
        for &shard_id in &self.hash_ring {
            if shard_id >= hash {
                return shard_id;
            }
        }
        // Wrap around to first shard
        self.hash_ring[0]
    }

    /// Get all context shards for a specific context
    pub fn get_context_shards(&self, context_id: &ActorId) -> Vec<&ContextShardConfig> {
        self.context_shards
            .iter()
            .filter(|s| &s.context_id == context_id)
            .collect()
    }
}

/// Disk storage configuration
const DISK_FILE_SIZE: u64 = 1024 * 1024 * 1024; // 1GB per file
const DISK_HEADER_SIZE: usize = 64; // Reserved space for file metadata
const DISK_ENTRY_HEADER_SIZE: usize = 16; // Size + timestamp + flags

/// Symbol identifier for interned data
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub struct Symbol(u64);

impl Symbol {
    /// Create new symbol from ID
    pub fn new(id: u64) -> Self {
        Self(id)
    }

    /// Get symbol ID
    pub fn id(&self) -> u64 {
        self.0
    }
}

/// Interned data entry in memory-mapped file
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
struct InternedEntry {
    hash: u64,           // Hash of the data for deduplication
    size: u32,           // Size of the serialized data
    timestamp: u64,      // When this entry was created
    data: Vec<u8>,       // The actual rkyv-serialized data
}

/// Memory-mapped file manager for universal interning
struct MappedFile {
    path: PathBuf,
    mmap: Option<MmapMut>,
    write_offset: u64,   // Current write position
    symbol_map: HashMap<u64, (u64, u32)>, // symbol_id -> (offset, size)
    hash_to_symbol: HashMap<u64, Symbol>, // hash -> symbol (for deduplication)
    next_symbol: AtomicU64,
}

impl MappedFile {
    /// Create new memory-mapped file
    async fn new(path: PathBuf) -> TamtilResult<Self> {
        // Ensure parent directory exists
        if let Some(parent) = path.parent() {
            tokio::fs::create_dir_all(parent).await
                .map_err(|e| TamtilError::Platform {
                    message: format!("Failed to create disk directory: {}", e)
                })?;
        }

        let mut file = Self {
            path,
            mmap: None,
            write_offset: DISK_HEADER_SIZE as u64,
            symbol_map: HashMap::new(),
            hash_to_symbol: HashMap::new(),
            next_symbol: AtomicU64::new(1),
        };

        // Initialize or load existing file
        file.init().await?;
        Ok(file)
    }

    /// Initialize or load existing memory-mapped file
    async fn init(&mut self) -> TamtilResult<()> {
        use std::fs::OpenOptions;

        // Create or open file
        let file = OpenOptions::new()
            .read(true)
            .write(true)
            .create(true)
            .open(&self.path)
            .map_err(|e| TamtilError::Platform {
                message: format!("Failed to open disk file: {}", e)
            })?;

        // Ensure file is at least 1GB
        let metadata = file.metadata().map_err(|e| TamtilError::Platform {
            message: format!("Failed to get file metadata: {}", e)
        })?;

        if metadata.len() < DISK_FILE_SIZE {
            file.set_len(DISK_FILE_SIZE).map_err(|e| TamtilError::Platform {
                message: format!("Failed to set file size: {}", e)
            })?;
        }

        // Create memory map
        let mmap = unsafe {
            MmapOptions::new()
                .map_mut(&file)
                .map_err(|e| TamtilError::Platform {
                    message: format!("Failed to create memory map: {}", e)
                })?
        };

        self.mmap = Some(mmap);

        // Load existing data if any
        self.load_index().await?;
        Ok(())
    }

    /// Load index from memory-mapped file header
    async fn load_index(&mut self) -> TamtilResult<()> {
        if let Some(ref mmap) = self.mmap {
            if mmap.len() >= DISK_HEADER_SIZE {
                // Read header to get metadata
                let header_bytes = &mmap[0..DISK_HEADER_SIZE];

                // First 8 bytes: write_offset
                // Next 8 bytes: next_symbol_id
                // Remaining: reserved
                if header_bytes.len() >= 16 {
                    let write_offset = u64::from_le_bytes([
                        header_bytes[0], header_bytes[1], header_bytes[2], header_bytes[3],
                        header_bytes[4], header_bytes[5], header_bytes[6], header_bytes[7],
                    ]);

                    let next_symbol = u64::from_le_bytes([
                        header_bytes[8], header_bytes[9], header_bytes[10], header_bytes[11],
                        header_bytes[12], header_bytes[13], header_bytes[14], header_bytes[15],
                    ]);

                    if write_offset > DISK_HEADER_SIZE as u64 && write_offset <= mmap.len() as u64 {
                        self.write_offset = write_offset;
                        self.next_symbol.store(next_symbol, Ordering::SeqCst);

                        // Rebuild index by scanning entries
                        self.rebuild_index().await?;

                        tracing::info!("Loaded interner index: {} symbols, write_offset: {}",
                                     self.symbol_map.len(), self.write_offset);
                    } else {
                        // Invalid header, start fresh
                        self.write_offset = DISK_HEADER_SIZE as u64;
                    }
                } else {
                    // No valid header, start fresh
                    self.write_offset = DISK_HEADER_SIZE as u64;
                }
            }
        }
        Ok(())
    }

    /// Rebuild symbol and hash maps by scanning the memory-mapped file
    async fn rebuild_index(&mut self) -> TamtilResult<()> {
        if let Some(ref mmap) = self.mmap {
            let mut offset = DISK_HEADER_SIZE as u64;

            while offset < self.write_offset {
                let start = offset as usize;
                if start + 4 > mmap.len() {
                    break;
                }

                // Read entry size (first 4 bytes of serialized data)
                let size_bytes = &mmap[start..start + 4];
                let entry_size = u32::from_le_bytes([
                    size_bytes[0], size_bytes[1], size_bytes[2], size_bytes[3]
                ]) as usize;

                if start + entry_size > mmap.len() {
                    break;
                }

                // Deserialize entry to get hash and symbol
                match rkyv::from_bytes::<InternedEntry, rkyv::rancor::Error>(&mmap[start..start + entry_size]) {
                    Ok(entry) => {
                        let symbol_id = self.next_symbol.fetch_add(1, Ordering::SeqCst);
                        let symbol = Symbol::new(symbol_id);

                        self.symbol_map.insert(symbol_id, (offset, entry_size as u32));
                        self.hash_to_symbol.insert(entry.hash, symbol);

                        offset += entry_size as u64;
                    }
                    Err(_) => {
                        // Corrupted entry, stop scanning
                        tracing::warn!("Corrupted entry at offset {}, stopping index rebuild", offset);
                        break;
                    }
                }
            }
        }
        Ok(())
    }

    /// Save index to memory-mapped file header
    async fn save_index(&mut self) -> TamtilResult<()> {
        if let Some(ref mut mmap) = self.mmap {
            if mmap.len() >= DISK_HEADER_SIZE {
                // Write header metadata
                let write_offset_bytes = self.write_offset.to_le_bytes();
                let next_symbol_bytes = self.next_symbol.load(Ordering::SeqCst).to_le_bytes();

                mmap[0..8].copy_from_slice(&write_offset_bytes);
                mmap[8..16].copy_from_slice(&next_symbol_bytes);

                // Flush header to disk
                mmap.flush().map_err(|e| TamtilError::Platform {
                    message: format!("Failed to flush header: {}", e)
                })?;
            }
        }
        Ok(())
    }

    /// Calculate hash for data deduplication
    fn hash_data(data: &[u8]) -> u64 {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        data.hash(&mut hasher);
        hasher.finish()
    }

    /// Check if file is near capacity
    fn near_capacity(&self) -> bool {
        self.write_offset >= DISK_FILE_SIZE * 90 / 100 // 90% full
    }

    /// Intern data and return symbol (universal deduplication)
    fn intern(&mut self, data: Vec<u8>) -> TamtilResult<Symbol> {
        let hash = Self::hash_data(&data);

        // Check if data already exists (deduplication)
        if let Some(&symbol) = self.hash_to_symbol.get(&hash) {
            return Ok(symbol);
        }

        // Create new symbol
        let symbol_id = self.next_symbol.fetch_add(1, Ordering::SeqCst);
        let symbol = Symbol::new(symbol_id);

        // Serialize the entry
        let entry = InternedEntry {
            hash,
            size: data.len() as u32,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            data,
        };

        let serialized = rkyv::to_bytes::<rkyv::rancor::Error>(&entry)
            .map_err(|e| TamtilError::Serialization {
                message: format!("Failed to serialize interned entry: {}", e)
            })?;

        // Write to memory-mapped file
        if let Some(ref mut mmap) = self.mmap {
            let entry_size = serialized.len();
            let write_pos = self.write_offset as usize;

            if write_pos + entry_size > mmap.len() {
                return Err(TamtilError::Platform {
                    message: "Memory-mapped file is full".to_string()
                });
            }

            // Write entry to memory-mapped region
            mmap[write_pos..write_pos + entry_size].copy_from_slice(&serialized);

            // Update indices
            self.symbol_map.insert(symbol_id, (self.write_offset, entry_size as u32));
            self.hash_to_symbol.insert(hash, symbol);
            self.write_offset += entry_size as u64;

            // Ensure data is persisted (msync equivalent)
            mmap.flush().map_err(|e| TamtilError::Platform {
                message: format!("Failed to flush memory map: {}", e)
            })?;
        }

        // Update header with new index information
        self.save_index().await?;

        Ok(symbol)
    }

    /// Get data by symbol (zero-copy access via rkyv archived data)
    fn get(&self, symbol: Symbol) -> TamtilResult<Option<Vec<u8>>> {
        if let Some(&(offset, size)) = self.symbol_map.get(&symbol.id()) {
            if let Some(ref mmap) = self.mmap {
                let start = offset as usize;
                let end = start + size as usize;

                if end <= mmap.len() {
                    // Deserialize entry to get the actual data
                    match rkyv::from_bytes::<InternedEntry, rkyv::rancor::Error>(&mmap[start..end]) {
                        Ok(entry) => Ok(Some(entry.data.clone())),
                        Err(e) => Err(TamtilError::Serialization {
                            message: format!("Failed to deserialize interned entry: {}", e)
                        })
                    }
                } else {
                    Ok(None)
                }
            } else {
                Ok(None)
            }
        } else {
            Ok(None)
        }
    }

    /// List all symbols
    fn symbols(&self) -> Vec<Symbol> {
        self.symbol_map.keys().map(|&id| Symbol::new(id)).collect()
    }
}

/// Distributed sharded interner leveraging platform/context/actor hierarchy
///
/// This replaces the old single-file universal interner with a distributed,
/// fault-tolerant system where each context forms its own shard cluster.
pub struct Disk {
    /// Base path for all storage
    base_path: PathBuf,
    /// Platform configuration for big orchestrator
    platform_config: Option<PlatformShardConfig>,
    /// OmniPaxos instances per context shard (one thread per core friendly)
    consensus_instances: Arc<RwLock<HashMap<String, Arc<RwLock<OmniPaxos<InternerEntry, ShardedInternerStorage>>>>>>,
    /// Actor symbol mappings (maintained for compatibility and fast access)
    actor_symbols: Arc<RwLock<HashMap<ActorId, HashMap<String, Symbol>>>>,
    /// Local memory-mapped files for zero-copy access (fallback for single-node)
    local_files: Arc<RwLock<HashMap<String, Arc<RwLock<MappedFile>>>>>,
    /// Current node configuration
    node_config: Option<NodeConfig>,
}

impl Disk {
    /// Create new distributed sharded interner (single-node mode)
    pub fn new(base_path: impl AsRef<Path>) -> Self {
        Self {
            base_path: base_path.as_ref().to_path_buf(),
            platform_config: None,
            consensus_instances: Arc::new(RwLock::new(HashMap::new())),
            actor_symbols: Arc::new(RwLock::new(HashMap::new())),
            local_files: Arc::new(RwLock::new(HashMap::new())),
            node_config: None,
        }
    }

    /// Create distributed interner with platform sharding configuration
    pub fn with_platform_config(
        base_path: impl AsRef<Path>,
        platform_config: PlatformShardConfig,
        node_config: NodeConfig,
    ) -> Self {
        Self {
            base_path: base_path.as_ref().to_path_buf(),
            platform_config: Some(platform_config),
            consensus_instances: Arc::new(RwLock::new(HashMap::new())),
            actor_symbols: Arc::new(RwLock::new(HashMap::new())),
            local_files: Arc::new(RwLock::new(HashMap::new())),
            node_config: Some(node_config),
        }
    }

    /// Initialize distributed consensus for a context shard
    pub async fn init_context_shard(&self, shard_config: &ContextShardConfig) -> TamtilResult<()> {
        if let Some(node_config) = &self.node_config {
            // Create sharded storage for this context
            let storage = ShardedInternerStorage::new(
                &self.base_path,
                node_config.node_id,
                shard_config.platform_id.clone(),
                shard_config.context_id.clone(),
                shard_config.shard_id,
            );

            // Initialize storage
            storage.load_state().await?;

            // Create OmniPaxos configuration
            let omnipaxos_config = OmniPaxosConfig {
                cluster_config: shard_config.to_omnipaxos_config(),
                server_config: omnipaxos::ServerConfig {
                    pid: node_config.node_id,
                    election_tick_timeout: 5,
                    ..Default::default()
                },
            };

            // Create OmniPaxos instance for this shard
            let omnipaxos = OmniPaxos::new(omnipaxos_config, storage);

            // Store consensus instance
            let shard_key = shard_config.shard_key();
            let mut instances = self.consensus_instances.write().await;
            instances.insert(shard_key.clone(), Arc::new(RwLock::new(omnipaxos)));

            tracing::info!("Initialized consensus for context shard: {}", shard_key);
        }
        Ok(())
    }

    /// Get the appropriate shard for an actor using platform orchestrator
    fn get_actor_shard(&self, actor_id: &ActorId) -> Option<String> {
        if let Some(platform_config) = &self.platform_config {
            platform_config.route_actor(actor_id)
                .map(|shard| shard.shard_key())
        } else {
            // Single-node mode: use default shard
            Some("default".to_string())
        }
    }

    /// Get universal interner file path (fallback for single-node)
    fn interner_path(&self) -> PathBuf {
        self.base_path.join("universal_interner.tamtil")
    }

    /// Get or create universal interner file
    async fn get_interner(&self) -> TamtilResult<Arc<RwLock<MappedFile>>> {
        let path = self.interner_path();
        let key = path.to_string_lossy().to_string();

        // Check if file already loaded
        {
            let files = self.files.read().await;
            if let Some(file) = files.get(&key) {
                return Ok(file.clone());
            }
        }

        // Create new universal interner file
        let mapped_file = MappedFile::new(path).await?;
        let file_arc = Arc::new(RwLock::new(mapped_file));

        // Store in cache
        {
            let mut files = self.files.write().await;
            files.insert(key, file_arc.clone());
        }

        Ok(file_arc)
    }

    /// Apply memory operations via distributed sharded interner
    pub async fn apply(&self, actor_id: &ActorId, operations: Vec<MemoryOperation>) -> TamtilResult<()> {
        if operations.is_empty() {
            return Ok(());
        }

        // Determine which shard this actor belongs to
        let shard_key = self.get_actor_shard(actor_id);

        if let Some(shard_key) = shard_key {
            // Check if we have a distributed consensus instance for this shard
            let instances = self.consensus_instances.read().await;
            if let Some(consensus_instance) = instances.get(&shard_key) {
                // Use distributed consensus for fault tolerance
                self.apply_via_consensus(actor_id, operations, consensus_instance.clone()).await
            } else {
                // Fallback to local storage (single-node mode)
                self.apply_local(actor_id, operations).await
            }
        } else {
            // Fallback to local storage
            self.apply_local(actor_id, operations).await
        }
    }

    /// Apply operations via distributed consensus (fault-tolerant)
    async fn apply_via_consensus(
        &self,
        actor_id: &ActorId,
        operations: Vec<MemoryOperation>,
        consensus_instance: Arc<RwLock<OmniPaxos<InternerEntry, ShardedInternerStorage>>>,
    ) -> TamtilResult<()> {
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        let node_id = self.node_config.as_ref().map(|c| c.node_id).unwrap_or(0);

        // Convert memory operations to consensus entries
        let mut consensus_entries = Vec::new();

        for operation in operations {
            match operation {
                MemoryOperation::Create { key, value } | MemoryOperation::Update { key, value } => {
                    // Serialize the memory value using rkyv
                    let serialized = rkyv::to_bytes::<rkyv::rancor::Error>(&value)
                        .map_err(|e| TamtilError::Serialization {
                            message: format!("Failed to serialize memory value: {}", e)
                        })?;

                    // Calculate hash for deduplication
                    let hash = MappedFile::hash_data(&serialized);

                    // Create store entry for consensus
                    consensus_entries.push(InternerEntry::Store {
                        hash,
                        data: serialized.to_vec(),
                        timestamp,
                        node_id,
                    });

                    // Create symbol mapping entry
                    let symbol = Symbol::new(hash); // Use hash as symbol for now
                    consensus_entries.push(InternerEntry::MapSymbol {
                        actor_id: actor_id.clone(),
                        key,
                        symbol,
                        timestamp,
                    });
                }

                MemoryOperation::Delete { key } => {
                    consensus_entries.push(InternerEntry::UnmapSymbol {
                        actor_id: actor_id.clone(),
                        key,
                        timestamp,
                    });
                }

                MemoryOperation::Link { from, to, relation } => {
                    let link_key = format!("__link__{}__{}__", from, relation);
                    let link_value = MemoryValue::string(to);
                    let serialized = rkyv::to_bytes::<rkyv::rancor::Error>(&link_value)
                        .map_err(|e| TamtilError::Serialization {
                            message: format!("Failed to serialize link: {}", e)
                        })?;

                    let hash = MappedFile::hash_data(&serialized);

                    consensus_entries.push(InternerEntry::Store {
                        hash,
                        data: serialized.to_vec(),
                        timestamp,
                        node_id,
                    });

                    let symbol = Symbol::new(hash);
                    consensus_entries.push(InternerEntry::MapSymbol {
                        actor_id: actor_id.clone(),
                        key: link_key,
                        symbol,
                        timestamp,
                    });
                }

                MemoryOperation::Unlink { from, relation, .. } => {
                    let link_key = format!("__link__{}__{}__", from, relation);
                    consensus_entries.push(InternerEntry::UnmapSymbol {
                        actor_id: actor_id.clone(),
                        key: link_key,
                        timestamp,
                    });
                }
            }
        }

        // Submit entries to consensus (this will replicate across the shard cluster)
        let mut consensus = consensus_instance.write().await;
        for entry in consensus_entries {
            consensus.append(entry).map_err(|e| TamtilError::Platform {
                message: format!("Failed to append to consensus log: {:?}", e)
            })?;
        }

        tracing::debug!("Actor {} applied {} operations via distributed consensus",
                       actor_id.as_str(), operations.len());

        Ok(())
    }

    /// Apply operations locally (single-node fallback)
    async fn apply_local(&self, actor_id: &ActorId, operations: Vec<MemoryOperation>) -> TamtilResult<()> {
        let interner = self.get_interner().await?;
        let mut mapped_file = interner.write().await;

        // Get or create actor's symbol mapping
        let mut actor_symbols = self.actor_symbols.write().await;
        let symbols = actor_symbols.entry(actor_id.clone()).or_insert_with(HashMap::new);

        let operation_count = operations.len();
        for operation in operations {
            match operation {
                MemoryOperation::Create { key, value } | MemoryOperation::Update { key, value } => {
                    // Serialize the memory value using rkyv
                    let serialized = rkyv::to_bytes::<rkyv::rancor::Error>(&value)
                        .map_err(|e| TamtilError::Serialization {
                            message: format!("Failed to serialize memory value: {}", e)
                        })?;

                    // Intern the data (universal deduplication)
                    let symbol = mapped_file.intern(serialized.to_vec())?;

                    // Store symbol mapping for this actor
                    symbols.insert(key, symbol);
                }

                MemoryOperation::Delete { key } => {
                    // Remove symbol mapping (data stays in interner for other actors)
                    symbols.remove(&key);
                }

                MemoryOperation::Link { from, to, relation } => {
                    // Store link as a special key with symbol
                    let link_key = format!("__link__{}__{}__", from, relation);
                    let link_value = MemoryValue::string(to);
                    let serialized = rkyv::to_bytes::<rkyv::rancor::Error>(&link_value)
                        .map_err(|e| TamtilError::Serialization {
                            message: format!("Failed to serialize link: {}", e)
                        })?;

                    let symbol = mapped_file.intern(serialized.to_vec())?;
                    symbols.insert(link_key, symbol);
                }

                MemoryOperation::Unlink { from, relation, .. } => {
                    // Remove link symbol mapping
                    let link_key = format!("__link__{}__{}__", from, relation);
                    symbols.remove(&link_key);
                }
            }
        }

        tracing::debug!("Actor {} applied {} memory operations via local interner",
                       actor_id.as_str(), operation_count);

        Ok(())
    }

    /// Get value from universal interner using actor's symbol mapping
    pub async fn get(&self, actor_id: &ActorId, key: &str) -> TamtilResult<Option<MemoryValue>> {
        let interner = self.get_interner().await?;
        let mapped_file = interner.read().await;

        // Get actor's symbol mapping
        let actor_symbols = self.actor_symbols.read().await;
        if let Some(symbols) = actor_symbols.get(actor_id) {
            if let Some(&symbol) = symbols.get(key) {
                // Get data from interner using symbol (zero-copy)
                if let Some(data) = mapped_file.get(symbol)? {
                    // Deserialize the memory value
                    match rkyv::from_bytes::<MemoryValue, rkyv::rancor::Error>(&data) {
                        Ok(value) => Ok(Some(value)),
                        Err(e) => Err(TamtilError::Serialization {
                            message: format!("Failed to deserialize memory value: {}", e)
                        }),
                    }
                } else {
                    Ok(None)
                }
            } else {
                Ok(None)
            }
        } else {
            Ok(None)
        }
    }

    /// List all keys for actor
    pub async fn keys(&self, actor_id: &ActorId) -> TamtilResult<Vec<String>> {
        let actor_symbols = self.actor_symbols.read().await;
        if let Some(symbols) = actor_symbols.get(actor_id) {
            Ok(symbols.keys().cloned().collect())
        } else {
            Ok(Vec::new())
        }
    }

    /// Get storage stats for actor
    pub async fn stats(&self, actor_id: &ActorId) -> TamtilResult<DiskStats> {
        let interner = self.get_interner().await?;
        let mapped_file = interner.read().await;

        let actor_symbols = self.actor_symbols.read().await;
        let entry_count = actor_symbols.get(actor_id)
            .map(|symbols| symbols.len())
            .unwrap_or(0);

        Ok(DiskStats {
            file_size: mapped_file.write_offset,
            entry_count,
            near_capacity: mapped_file.near_capacity(),
        })
    }
}

/// Disk storage statistics
#[derive(Debug, Clone)]
pub struct DiskStats {
    pub file_size: u64,
    pub entry_count: usize,
    pub near_capacity: bool,
}

impl Clone for Disk {
    fn clone(&self) -> Self {
        Self {
            base_path: self.base_path.clone(),
            files: self.files.clone(),
            actor_symbols: self.actor_symbols.clone(),
        }
    }
}

/// Actor identifier with hierarchical URL-based addressing
///
/// Supports the full hierarchy:
/// - platform.com
/// - platform.com/context_name
/// - platform.com/context_name/context_id
/// - platform.com/context_name/context_id/actor_name
/// - platform.com/context_name/context_id/actor_name/actor_id
/// - platform.com/context_name/context_id/actor_name/actor_id/child_name
/// - platform.com/context_name/context_id/actor_name/actor_id/child_name/child_id
/// - etc...
#[derive(Debug, Clone, PartialEq, Eq, Hash, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub struct ActorId(String);

impl ActorId {
    /// Create new actor ID
    pub fn new(id: impl Into<String>) -> Self {
        Self(id.into())
    }

    /// Get string representation
    pub fn as_str(&self) -> &str {
        &self.0
    }

    /// Create child ID by appending name to current ID
    pub fn child(&self, name: impl Into<String>) -> Self {
        Self(format!("{}/{}", self.0, name.into()))
    }

    /// Get parent ID by removing last segment
    pub fn parent(&self) -> Option<Self> {
        self.0.rfind('/').map(|pos| Self(self.0[..pos].to_string()))
    }

    /// Get depth level (number of segments)
    pub fn depth(&self) -> usize {
        if self.0.is_empty() {
            0
        } else {
            self.0.matches('/').count() + 1
        }
    }

    /// Check if this ID is child of another ID
    pub fn child_of(&self, parent: &ActorId) -> bool {
        self.0.starts_with(&parent.0) &&
        self.0.len() > parent.0.len() &&
        self.0.chars().nth(parent.0.len()) == Some('/')
    }
}

impl From<&str> for ActorId {
    fn from(s: &str) -> Self {
        Self(s.to_string())
    }
}

impl From<String> for ActorId {
    fn from(s: String) -> Self {
        Self(s)
    }
}

/// Core trait that all TAMTIL actors must implement
///
/// Actors receive actions and return reactions, with access to:
/// - `actors` registry for spawning children and communicating with other actors
/// - `memories` registry for managing actor state through reactions
#[async_trait]
pub trait Actor: Send + Sync + 'static {
    /// Action type this actor handles
    type Action: Archive + for<'a> Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, RancorError>> + Send + Sync + 'static;

    /// Reaction type this actor produces
    type Reaction: Reaction + Clone;

    /// Handle action and return reaction
    ///
    /// Actors can:
    /// - Spawn children and communicate via `actors.spawn(name)` and `actors.actor(id).act(action)`
    /// - Manage state via `memories.remember(reaction)` and `memories.recall(query)`
    async fn act(&mut self, action: Self::Action, actors: &Actors, memories: &Memories) -> TamtilResult<Self::Reaction>;
}

/// Actors registry for spawning and communicating with child actors
///
/// Provides the developer API for:
/// - Spawning child actors via `spawn(name)`
/// - Communicating with actors via `actor(id).act(action)`
/// - Automatic hierarchical addressing
#[derive(Clone, Default)]
pub struct Actors {
    /// Parent actor ID for hierarchical addressing
    parent: Option<ActorId>,
}

impl Actors {
    /// Create new actors registry
    pub fn new() -> Self {
        Self { parent: None }
    }

    /// Create actors registry for specific parent
    pub fn child(parent: ActorId) -> Self {
        Self { parent: Some(parent) }
    }

    /// Get actor proxy for communication
    pub fn actor(&self, id: impl Into<ActorId>) -> ActorProxy {
        ActorProxy { id: id.into() }
    }

    /// Spawn new child actor with hierarchical addressing
    ///
    /// Creates child ID by appending name to parent ID:
    /// - Parent: "platform.com/web/main"
    /// - Child name: "counter"
    /// - Result: "platform.com/web/main/counter"
    pub async fn spawn(&self, name: impl Into<String>) -> TamtilResult<ActorId> {
        let child_id = self.build(name.into());

        // In real implementation, would spawn actual actor
        // For now, just return hierarchical ID
        tracing::info!("Would spawn child actor: {}", child_id.as_str());

        Ok(child_id)
    }

    /// Build hierarchical child ID based on parent
    fn build(&self, name: String) -> ActorId {
        match &self.parent {
            Some(parent_id) => parent_id.child(name),
            None => ActorId::new(name),
        }
    }
}

/// Proxy for actor communication
///
/// Provides the `actors.actor(id).act(action)` interface
pub struct ActorProxy {
    id: ActorId,
}

impl ActorProxy {
    /// Send action to actor (simplified for demo)
    ///
    /// In real implementation, would route to actual actor based on ID
    pub async fn act<A, R>(&self, _action: A) -> TamtilResult<R>
    where
        A: Archive + for<'a> Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, RancorError>> + Send + Sync + 'static,
        R: Archive + for<'a> Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, RancorError>> + Send + Sync + 'static + Default,
    {
        tracing::debug!("Would send action to {}", self.id.as_str());
        Ok(R::default())
    }

    /// Get actor ID
    pub fn id(&self) -> &ActorId {
        &self.id
    }
}

// ============================================================================
// STANDARD ACTIONS AND REACTIONS
// ============================================================================

/// Standard actions that all actors support
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum StandardAction {
    Start,
    Stop,
    Shutdown,
}

/// Standard reactions that all actors produce
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum StandardReaction {
    Started,
    Stopped,
    Shutdown,
}

// ============================================================================
// PLATFORM MODULE
// ============================================================================

pub mod platform {
    use super::*;

    /// Actions for platform actors
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum Action {
        Standard(StandardAction),
        StartContext { name: String },
        StopContext { name: String },
        RecoverState, // New action for crash recovery
    }

    /// Reactions from platform actors
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum Reaction {
        Standard(StandardReaction),
        ContextStarted { id: String },
        ContextStopped { id: String },
        ContextNotFound { name: String },
        StateRecovered { contexts: Vec<String> }, // New reaction for crash recovery
    }

    #[async_trait]
    impl crate::Reaction for Reaction {
        async fn remember(&self) -> TamtilResult<Vec<MemoryOperation>> {
            let mut operations = Vec::new();

            match self {
                Reaction::Standard(StandardReaction::Started) => {
                    // Remember platform startup
                    operations.push(MemoryOperation::Create {
                        key: "platform:state".to_string(),
                        value: MemoryValue::string("started"),
                    });
                    operations.push(MemoryOperation::Create {
                        key: "platform:startup_time".to_string(),
                        value: MemoryValue::number(
                            std::time::SystemTime::now()
                                .duration_since(std::time::UNIX_EPOCH)
                                .unwrap_or_default()
                                .as_secs() as f64
                        ),
                    });
                }

                Reaction::ContextStarted { id } => {
                    // Remember context was started
                    operations.push(MemoryOperation::Create {
                        key: format!("platform:context:{}", id),
                        value: MemoryValue::string("started"),
                    });
                    // Add to active contexts list
                    operations.push(MemoryOperation::Create {
                        key: format!("platform:contexts:active:{}", id),
                        value: MemoryValue::string(id.clone()),
                    });
                }

                Reaction::ContextStopped { id } => {
                    // Remove from active contexts
                    operations.push(MemoryOperation::Delete {
                        key: format!("platform:contexts:active:{}", id),
                    });
                    operations.push(MemoryOperation::Update {
                        key: format!("platform:context:{}", id),
                        value: MemoryValue::string("stopped"),
                    });
                }

                Reaction::StateRecovered { contexts } => {
                    // Remember successful state recovery
                    operations.push(MemoryOperation::Create {
                        key: "platform:last_recovery".to_string(),
                        value: MemoryValue::number(
                            std::time::SystemTime::now()
                                .duration_since(std::time::UNIX_EPOCH)
                                .unwrap_or_default()
                                .as_secs() as f64
                        ),
                    });
                    operations.push(MemoryOperation::Create {
                        key: "platform:recovered_contexts_count".to_string(),
                        value: MemoryValue::number(contexts.len() as f64),
                    });
                }

                _ => {
                    // Other reactions don't need persistence
                }
            }

            Ok(operations)
        }
    }

    /// Platform actor manages context actors
    ///
    /// Responsible for:
    /// - Spawning and managing context actors
    /// - Hierarchical addressing at platform level
    /// - Lifecycle management of contexts
    pub struct Actor {
        id: ActorId,
        contexts: HashMap<String, ActorId>, // name -> full_id mapping
    }

    impl Actor {
        /// Create new platform actor
        pub fn new(id: impl Into<ActorId>) -> Self {
            Self {
                id: id.into(),
                contexts: HashMap::new(),
            }
        }

        /// Recover state from universal interner following "let it crash" pattern
        async fn recover_state(&mut self, memories: &Memories) -> TamtilResult<Vec<String>> {
            let mut recovered_contexts = Vec::new();

            // Query for active contexts from universal interner
            let active_context_keys = memories.recall("platform:contexts:active:").await?;

            for memory_value in active_context_keys {
                if let MemoryValue::String(context_id) = memory_value {
                    // Extract context name from the full ID
                    if let Some(context_name) = self.extract_context_name(&context_id) {
                        // Restore context in our local state
                        let full_context_id = ActorId::new(context_id.clone());
                        self.contexts.insert(context_name.clone(), full_context_id);
                        recovered_contexts.push(context_name.clone());

                        tracing::debug!("Platform {} recovered context: {} -> {}",
                                       self.id.as_str(), context_name, context_id);
                    }
                }
            }

            Ok(recovered_contexts)
        }

        /// Extract context name from full context ID
        fn extract_context_name(&self, context_id: &str) -> Option<String> {
            // For hierarchical IDs like "platform.com/web", extract "web"
            context_id.split('/').last().map(|s| s.to_string())
        }
    }

    #[async_trait]
    impl crate::Actor for Actor {
        type Action = Action;
        type Reaction = Reaction;

        async fn act(&mut self, action: Self::Action, actors: &Actors, memories: &Memories) -> TamtilResult<Self::Reaction> {
            match action {
                Action::Standard(StandardAction::Start) => {
                    tracing::info!("Platform {} starting", self.id.as_str());

                    // Automatically attempt state recovery on start
                    if let Ok(recovered_contexts) = self.recover_state(memories).await {
                        if !recovered_contexts.is_empty() {
                            tracing::info!("Platform {} recovered {} contexts from universal interner",
                                         self.id.as_str(), recovered_contexts.len());
                            return Ok(Reaction::StateRecovered { contexts: recovered_contexts });
                        }
                    }

                    Ok(Reaction::Standard(StandardReaction::Started))
                }

                Action::RecoverState => {
                    tracing::info!("Platform {} performing explicit state recovery", self.id.as_str());
                    let recovered_contexts = self.recover_state(memories).await?;
                    Ok(Reaction::StateRecovered { contexts: recovered_contexts })
                }

                Action::Standard(StandardAction::Stop) => {
                    tracing::info!("Platform {} stopping", self.id.as_str());
                    // Stop all contexts - children auto-stop
                    self.contexts.clear();
                    Ok(Reaction::Standard(StandardReaction::Stopped))
                }

                Action::Standard(StandardAction::Shutdown) => {
                    tracing::info!("Platform {} shutting down", self.id.as_str());
                    self.contexts.clear();
                    Ok(Reaction::Standard(StandardReaction::Shutdown))
                }

                Action::StartContext { name } => {
                    if self.contexts.contains_key(&name) {
                        let id = self.contexts[&name].as_str().to_string();
                        return Ok(Reaction::ContextStarted { id });
                    }

                    // Spawn context actor as child
                    let context_id = actors.spawn(name.clone()).await?;
                    self.contexts.insert(name, context_id.clone());

                    tracing::info!("Platform {} started context {}", self.id.as_str(), context_id.as_str());
                    Ok(Reaction::ContextStarted { id: context_id.as_str().to_string() })
                }

                Action::StopContext { name } => {
                    if let Some(context_id) = self.contexts.remove(&name) {
                        tracing::info!("Platform {} stopped context {}", self.id.as_str(), context_id.as_str());
                        Ok(Reaction::ContextStopped { id: context_id.as_str().to_string() })
                    } else {
                        Ok(Reaction::ContextNotFound { name })
                    }
                }
            }
        }
    }
}

// ============================================================================
// CONTEXT MODULE
// ============================================================================

pub mod context {
    use super::*;

    /// Actions for context actors
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum Action {
        Standard(StandardAction),
        StartActor { name: String },
        StopActor { name: String },
        RecoverState, // New action for crash recovery
    }

    /// Reactions from context actors
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum Reaction {
        Standard(StandardReaction),
        ActorStarted { id: String },
        ActorStopped { id: String },
        ActorNotFound { name: String },
        StateRecovered { actors: Vec<String> }, // New reaction for crash recovery
    }

    #[async_trait]
    impl crate::Reaction for Reaction {
        async fn remember(&self) -> TamtilResult<Vec<MemoryOperation>> {
            let mut operations = Vec::new();

            match self {
                Reaction::Standard(StandardReaction::Started) => {
                    // Remember context startup
                    operations.push(MemoryOperation::Create {
                        key: "context:state".to_string(),
                        value: MemoryValue::string("started"),
                    });
                    operations.push(MemoryOperation::Create {
                        key: "context:startup_time".to_string(),
                        value: MemoryValue::number(
                            std::time::SystemTime::now()
                                .duration_since(std::time::UNIX_EPOCH)
                                .unwrap_or_default()
                                .as_secs() as f64
                        ),
                    });
                }

                Reaction::ActorStarted { id } => {
                    // Remember actor was started
                    operations.push(MemoryOperation::Create {
                        key: format!("context:actor:{}", id),
                        value: MemoryValue::string("started"),
                    });
                    // Add to active actors list
                    operations.push(MemoryOperation::Create {
                        key: format!("context:actors:active:{}", id),
                        value: MemoryValue::string(id.clone()),
                    });
                }

                Reaction::ActorStopped { id } => {
                    // Remove from active actors
                    operations.push(MemoryOperation::Delete {
                        key: format!("context:actors:active:{}", id),
                    });
                    operations.push(MemoryOperation::Update {
                        key: format!("context:actor:{}", id),
                        value: MemoryValue::string("stopped"),
                    });
                }

                Reaction::StateRecovered { actors } => {
                    // Remember successful state recovery
                    operations.push(MemoryOperation::Create {
                        key: "context:last_recovery".to_string(),
                        value: MemoryValue::number(
                            std::time::SystemTime::now()
                                .duration_since(std::time::UNIX_EPOCH)
                                .unwrap_or_default()
                                .as_secs() as f64
                        ),
                    });
                    operations.push(MemoryOperation::Create {
                        key: "context:recovered_actors_count".to_string(),
                        value: MemoryValue::number(actors.len() as f64),
                    });
                }

                _ => {
                    // Other reactions don't need persistence
                }
            }

            Ok(operations)
        }
    }

    /// Context actor manages regular actors
    ///
    /// Responsible for:
    /// - Spawning and managing regular actors
    /// - Hierarchical addressing at context level
    /// - Lifecycle management of actors
    pub struct Actor {
        id: ActorId,
        actors: HashMap<String, ActorId>, // name -> full_id mapping
    }

    impl Actor {
        /// Create new context actor
        pub fn new(id: impl Into<ActorId>) -> Self {
            Self {
                id: id.into(),
                actors: HashMap::new(),
            }
        }

        /// Recover state from universal interner following "let it crash" pattern
        async fn recover_state(&mut self, memories: &Memories) -> TamtilResult<Vec<String>> {
            let mut recovered_actors = Vec::new();

            // Query for active actors from universal interner
            let active_actor_keys = memories.recall("context:actors:active:").await?;

            for memory_value in active_actor_keys {
                if let MemoryValue::String(actor_id) = memory_value {
                    // Extract actor name from the full ID
                    if let Some(actor_name) = self.extract_actor_name(&actor_id) {
                        // Restore actor in our local state
                        let full_actor_id = ActorId::new(actor_id.clone());
                        self.actors.insert(actor_name.clone(), full_actor_id);
                        recovered_actors.push(actor_name.clone());

                        tracing::debug!("Context {} recovered actor: {} -> {}",
                                       self.id.as_str(), actor_name, actor_id);
                    }
                }
            }

            Ok(recovered_actors)
        }

        /// Extract actor name from full actor ID
        fn extract_actor_name(&self, actor_id: &str) -> Option<String> {
            // For hierarchical IDs like "platform.com/web/main/todos", extract "todos"
            actor_id.split('/').last().map(|s| s.to_string())
        }
    }

    #[async_trait]
    impl crate::Actor for Actor {
        type Action = Action;
        type Reaction = Reaction;

        async fn act(&mut self, action: Self::Action, actors: &Actors, memories: &Memories) -> TamtilResult<Self::Reaction> {
            match action {
                Action::Standard(StandardAction::Start) => {
                    tracing::info!("Context {} starting", self.id.as_str());

                    // Automatically attempt state recovery on start
                    if let Ok(recovered_actors) = self.recover_state(memories).await {
                        if !recovered_actors.is_empty() {
                            tracing::info!("Context {} recovered {} actors from universal interner",
                                         self.id.as_str(), recovered_actors.len());
                            return Ok(Reaction::StateRecovered { actors: recovered_actors });
                        }
                    }

                    Ok(Reaction::Standard(StandardReaction::Started))
                }

                Action::RecoverState => {
                    tracing::info!("Context {} performing explicit state recovery", self.id.as_str());
                    let recovered_actors = self.recover_state(memories).await?;
                    Ok(Reaction::StateRecovered { actors: recovered_actors })
                }

                Action::Standard(StandardAction::Stop) => {
                    tracing::info!("Context {} stopping", self.id.as_str());
                    // Stop all actors - children auto-stop
                    self.actors.clear();
                    Ok(Reaction::Standard(StandardReaction::Stopped))
                }

                Action::Standard(StandardAction::Shutdown) => {
                    tracing::info!("Context {} shutting down", self.id.as_str());
                    self.actors.clear();
                    Ok(Reaction::Standard(StandardReaction::Shutdown))
                }

                Action::StartActor { name } => {
                    if self.actors.contains_key(&name) {
                        let id = self.actors[&name].as_str().to_string();
                        return Ok(Reaction::ActorStarted { id });
                    }

                    // Spawn actor as child
                    let actor_id = actors.spawn(name.clone()).await?;
                    self.actors.insert(name, actor_id.clone());

                    tracing::info!("Context {} started actor {}", self.id.as_str(), actor_id.as_str());
                    Ok(Reaction::ActorStarted { id: actor_id.as_str().to_string() })
                }

                Action::StopActor { name } => {
                    if let Some(actor_id) = self.actors.remove(&name) {
                        tracing::info!("Context {} stopped actor {}", self.id.as_str(), actor_id.as_str());
                        Ok(Reaction::ActorStopped { id: actor_id.as_str().to_string() })
                    } else {
                        Ok(Reaction::ActorNotFound { name })
                    }
                }
            }
        }
    }
}

// ============================================================================
// ACTOR MODULE
// ============================================================================

pub mod actor {
    use super::*;

    /// Example counter action using rkyv
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum CounterAction {
        Start,
        Stop,
        Increment,
        Get,
        Spawn { name: String }, // Spawn child actor
    }

    /// Example counter reaction using rkyv
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum CounterReaction {
        Started,
        Stopped,
        Incremented { count: u32 },
        Count { value: u32 },
        Spawned { child: String }, // Child spawned
    }

    #[async_trait]
    impl crate::Reaction for CounterReaction {
        async fn remember(&self) -> TamtilResult<Vec<MemoryOperation>> {
            let mut operations = Vec::new();

            match self {
                CounterReaction::Incremented { count } => {
                    // Store the current count in memory
                    operations.push(MemoryOperation::Update {
                        key: "counter:value".to_string(),
                        value: MemoryValue::number(*count as f64),
                    });
                }
                CounterReaction::Started => {
                    // Initialize counter value
                    operations.push(MemoryOperation::Create {
                        key: "counter:value".to_string(),
                        value: MemoryValue::number(0.0),
                    });
                }
                _ => {
                    // Other reactions don't need memory operations
                }
            }

            Ok(operations)
        }
    }

    /// Example counter actor implementation
    ///
    /// Demonstrates:
    /// - Basic state management (count)
    /// - Child actor spawning via actors.spawn()
    /// - Inter-actor communication via actors.actor().act()
    pub struct Counter {
        id: ActorId,
        count: u32,
    }

    impl Counter {
        /// Create new counter actor
        pub fn new(id: impl Into<ActorId>) -> Self {
            Self {
                id: id.into(),
                count: 0,
            }
        }
    }

    #[async_trait]
    impl crate::Actor for Counter {
        type Action = CounterAction;
        type Reaction = CounterReaction;

        async fn act(&mut self, action: Self::Action, actors: &Actors, _memories: &Memories) -> TamtilResult<Self::Reaction> {
            match action {
                CounterAction::Start => {
                    tracing::info!("Counter {} starting", self.id.as_str());
                    Ok(CounterReaction::Started)
                }

                CounterAction::Stop => {
                    tracing::info!("Counter {} stopping", self.id.as_str());
                    // Children auto-stop when parent stops
                    Ok(CounterReaction::Stopped)
                }

                CounterAction::Increment => {
                    self.count += 1;
                    tracing::debug!("Counter {} incremented to {}", self.id.as_str(), self.count);

                    // Example inter-actor communication:
                    // actors.actor("platform.com/web/main/other").act(CounterAction::Get).await?;

                    Ok(CounterReaction::Incremented { count: self.count })
                }

                CounterAction::Get => {
                    Ok(CounterReaction::Count { value: self.count })
                }

                CounterAction::Spawn { name } => {
                    // Spawn child actor using hierarchical addressing
                    let child_id = actors.spawn(name.clone()).await?;
                    tracing::info!("Counter {} spawned child: {}", self.id.as_str(), child_id.as_str());

                    // Could also communicate with child:
                    // actors.actor(child_id).act(CounterAction::Start).await?;

                    Ok(CounterReaction::Spawned { child: child_id.as_str().to_string() })
                }
            }
        }
    }
}

// ============================================================================
// CHAPTER 2: INFRASTRUCTURE - Alice Ryhl's Actor Pattern Implementation
// ============================================================================

/// Message envelope for actor communication following Alice Ryhl's pattern
///
/// This enum represents the two types of messages an actor can receive:
/// - Action messages that expect a response
/// - Shutdown messages that terminate the actor
pub enum ActorMessage<A, R> {
    Action {
        action: A,
        respond_to: Option<oneshot::Sender<TamtilResult<R>>>,
    },
    Shutdown,
}

/// Actor task that runs independently following Alice Ryhl's pattern
///
/// This is the "task" part of the actor - it runs in its own tokio task
/// and processes messages from the handle via an mpsc channel
pub struct ActorTask<T: Actor> {
    id: ActorId,
    actor: T,
    receiver: mpsc::Receiver<ActorMessage<T::Action, T::Reaction>>,
    actors: Actors,
    memories: Memories,
}

impl<T: Actor> ActorTask<T> {
    /// Create new actor task
    pub fn new(
        id: ActorId,
        actor: T,
        receiver: mpsc::Receiver<ActorMessage<T::Action, T::Reaction>>,
        actors: Actors,
        memories: Memories,
    ) -> Self {
        Self { id, actor, receiver, actors, memories }
    }

    /// Handle single message from the channel
    async fn handle_message(&mut self, msg: ActorMessage<T::Action, T::Reaction>) -> TamtilResult<bool> {
        match msg {
            ActorMessage::Action { action, respond_to } => {
                // Call the actor's act method with the actors registry and memories
                let result = self.actor.act(action, &self.actors, &self.memories).await;

                // If successful, remember the reaction
                if let Ok(ref reaction) = result {
                    let _ = self.memories.remember(reaction.clone()).await;
                }

                // Send response back if requested
                if let Some(sender) = respond_to {
                    let _ = sender.send(result);
                }
                Ok(true) // Continue running
            }
            ActorMessage::Shutdown => {
                tracing::info!("Actor {} received shutdown signal", self.id.as_str());
                Ok(false) // Stop running
            }
        }
    }
}

/// Run actor task (following Alice Ryhl's pattern)
///
/// This function runs the main message loop for an actor task.
/// It continues until shutdown is requested or an error occurs.
pub async fn run_actor_task<T: Actor>(mut task: ActorTask<T>) -> TamtilResult<()> {
    tracing::info!("Actor {} starting task", task.id.as_str());

    // Main message loop - process messages until shutdown
    while let Some(msg) = task.receiver.recv().await {
        match task.handle_message(msg).await {
            Ok(true) => continue,  // Keep running
            Ok(false) => break,    // Shutdown requested
            Err(e) => {
                tracing::error!("Actor {} error: {}", task.id.as_str(), e);
                break;
            }
        }
    }

    tracing::info!("Actor {} task stopped", task.id.as_str());
    Ok(())
}

/// Typed actor handle for type-safe communication
#[derive(Clone)]
pub struct ActorHandle<T: Actor> {
    id: ActorId,
    sender: mpsc::Sender<ActorMessage<T::Action, T::Reaction>>,
}

impl<T: Actor> ActorHandle<T> {
    /// Create new actor handle
    pub fn new(id: ActorId, sender: mpsc::Sender<ActorMessage<T::Action, T::Reaction>>) -> Self {
        Self { id, sender }
    }

    /// Send action to actor and wait for reaction
    pub async fn act(&self, action: T::Action) -> TamtilResult<T::Reaction> {
        let (send, recv) = oneshot::channel();

        let msg = ActorMessage::Action {
            action,
            respond_to: Some(send),
        };

        self.sender.send(msg).await
            .map_err(|_| TamtilError::Communication {
                message: "Failed to send action to actor".to_string()
            })?;

        recv.await
            .map_err(|_| TamtilError::Communication {
                message: "Actor task has been killed".to_string()
            })?
    }

    /// Get actor ID
    pub fn id(&self) -> &ActorId {
        &self.id
    }

    /// Shutdown actor
    pub async fn shutdown(&self) -> TamtilResult<()> {
        self.sender.send(ActorMessage::Shutdown).await
            .map_err(|_| TamtilError::Communication {
                message: "Failed to send shutdown message".to_string()
            })?;
        Ok(())
    }
}

/// Platform manages actor system and hides underlying runtime
///
/// The platform acts as the big orchestrator managing multiple context shards,
/// enabling natural distribution and fault tolerance through OmniPaxos consensus.
pub struct Platform {
    disk: Disk,
    platform_id: ActorId,
}

impl Platform {
    /// Create new platform (single-node mode)
    pub fn new() -> Self {
        let platform_id = ActorId::new("platform.local");
        let disk = Disk::new("./tamtil_data");
        Self { disk, platform_id }
    }

    /// Create new platform with custom disk path (single-node mode)
    pub fn with_disk_path(path: impl AsRef<Path>) -> Self {
        let platform_id = ActorId::new("platform.local");
        let disk = Disk::new(path);
        Self { disk, platform_id }
    }

    /// Create distributed platform with sharding configuration
    pub fn with_sharding(
        platform_id: impl Into<ActorId>,
        base_path: impl AsRef<Path>,
        platform_config: PlatformShardConfig,
        node_config: NodeConfig,
    ) -> Self {
        let platform_id = platform_id.into();
        let disk = Disk::with_platform_config(base_path, platform_config, node_config);
        Self { disk, platform_id }
    }

    /// Initialize context shards for distributed operation
    pub async fn init_context_shards(&self, shard_configs: Vec<ContextShardConfig>) -> TamtilResult<()> {
        for shard_config in shard_configs {
            self.disk.init_context_shard(&shard_config).await?;
        }

        tracing::info!("Platform {} initialized with {} context shards",
                      self.platform_id.as_str(), shard_configs.len());
        Ok(())
    }

    /// Get platform ID
    pub fn platform_id(&self) -> &ActorId {
        &self.platform_id
    }

    /// Spawn actor on platform
    pub async fn spawn<T: Actor + 'static>(
        &self,
        id: impl Into<ActorId>,
        actor: T,
    ) -> TamtilResult<ActorHandle<T>> {
        let actor_id = id.into();
        let (sender, receiver) = mpsc::channel(32);

        // Create actors registry for this actor
        let actors = Actors::child(actor_id.clone());

        // Create memories registry for this actor
        let memories = Memories::new(actor_id.clone(), self.disk.clone());

        let task = ActorTask::new(actor_id.clone(), actor, receiver, actors, memories);
        tokio::spawn(run_actor_task(task));

        let handle = ActorHandle::new(actor_id, sender);
        Ok(handle)
    }
}

impl Default for Platform {
    fn default() -> Self {
        Self::new()
    }
}

// ============================================================================
// CHAPTER 3: TODO LIST APPLICATION - Complete TAMTIL Feature Showcase
// ============================================================================

/// Todo List Application demonstrating all TAMTIL features
///
/// This comprehensive example showcases:
/// - Hierarchical addressing: todoapp.com/web/main/todos/list/item/item_id
/// - Actor spawning via actors.spawn(name)
/// - Inter-actor communication via actors.actor(id).act(action)
/// - Automatic child cleanup when parent stops
/// - rkyv zero-copy serialization
/// - Action→reaction pattern throughout
/// - Single-word naming convention
/// - Real-world application patterns
pub mod todoapp {
    use super::*;

    /// Todo item data structure
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub struct Todo {
        pub id: u32,
        pub title: String,
        pub description: String,
        pub completed: bool,
        pub priority: Priority,
        pub created_at: u64, // timestamp
    }

    /// Priority levels for todos
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum Priority {
        Low,
        Medium,
        High,
        Critical,
    }

    /// Actions for todo list management
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum TodoAction {
        // Standard lifecycle
        Start,
        Stop,

        // Todo management
        Create { title: String, description: String, priority: Priority },
        Complete { id: u32 },
        Update { id: u32, title: Option<String>, description: Option<String>, priority: Option<Priority> },
        Delete { id: u32 },
        List,
        Get { id: u32 },

        // Filtering and search
        Filter { completed: Option<bool>, priority: Option<Priority> },
        Search { query: String },

        // Statistics and analytics
        Stats,

        // Child actor management
        SpawnAnalytics,
        SpawnNotifier,
    }

    /// Reactions from todo list operations
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum TodoReaction {
        // Standard lifecycle
        Started,
        Stopped,

        // Todo operations
        Created { todo: Todo },
        Completed { id: u32 },
        Updated { todo: Todo },
        Deleted { id: u32 },
        Found { todo: Todo },
        NotFound { id: u32 },

        // List operations
        TodoList { todos: Vec<Todo> },
        FilteredList { todos: Vec<Todo>, filter: String },
        SearchResults { todos: Vec<Todo>, query: String },

        // Statistics
        Statistics { total: u32, completed: u32, pending: u32, by_priority: Vec<(String, u32)> },

        // Child management
        AnalyticsSpawned { id: String },
        NotifierSpawned { id: String },

        // Errors
        Error { message: String },
    }

    #[async_trait]
    impl Reaction for TodoReaction {
        async fn remember(&self) -> TamtilResult<Vec<MemoryOperation>> {
            let mut operations = Vec::new();

            match self {
                TodoReaction::Created { todo } => {
                    // Store the todo ID in memory
                    operations.push(MemoryOperation::Create {
                        key: format!("todo:{}", todo.id),
                        value: MemoryValue::number(todo.id as f64),
                    });

                    // Store todo title
                    operations.push(MemoryOperation::Create {
                        key: format!("todo:{}:title", todo.id),
                        value: MemoryValue::string(todo.title.clone()),
                    });

                    // Store todo description
                    operations.push(MemoryOperation::Create {
                        key: format!("todo:{}:description", todo.id),
                        value: MemoryValue::string(todo.description.clone()),
                    });

                    // Store completion status
                    operations.push(MemoryOperation::Create {
                        key: format!("todo:{}:completed", todo.id),
                        value: MemoryValue::boolean(todo.completed),
                    });

                    // Update statistics
                    operations.push(MemoryOperation::Update {
                        key: "stats:total".to_string(),
                        value: MemoryValue::number(1.0), // This would be incremented in real implementation
                    });
                }

                TodoReaction::Completed { id } => {
                    // Update the todo's completed status
                    operations.push(MemoryOperation::Update {
                        key: format!("todo:{}:completed", id),
                        value: MemoryValue::boolean(true),
                    });

                    // Update completion statistics
                    operations.push(MemoryOperation::Update {
                        key: "stats:completed".to_string(),
                        value: MemoryValue::number(1.0), // This would be incremented in real implementation
                    });
                }

                TodoReaction::Deleted { id } => {
                    // Remove the todo from memory
                    operations.push(MemoryOperation::Delete {
                        key: format!("todo:{}", id),
                    });
                }

                TodoReaction::Started => {
                    // Initialize statistics
                    operations.push(MemoryOperation::Create {
                        key: "stats:total".to_string(),
                        value: MemoryValue::number(0.0),
                    });
                    operations.push(MemoryOperation::Create {
                        key: "stats:completed".to_string(),
                        value: MemoryValue::number(0.0),
                    });
                }

                _ => {
                    // Other reactions don't need memory operations for now
                }
            }

            Ok(operations)
        }
    }

    /// Main todo list actor
    pub struct TodoList {
        id: ActorId,
        todos: HashMap<u32, Todo>,
        next_id: u32,
        analytics_id: Option<ActorId>,
        notifier_id: Option<ActorId>,
    }

    impl TodoList {
        /// Create new todo list actor
        pub fn new(id: impl Into<ActorId>) -> Self {
            Self {
                id: id.into(),
                todos: HashMap::new(),
                next_id: 1,
                analytics_id: None,
                notifier_id: None,
            }
        }

        /// Generate statistics for current todos
        fn generate_stats(&self) -> TodoReaction {
            let total = self.todos.len() as u32;
            let completed = self.todos.values().filter(|t| t.completed).count() as u32;
            let pending = total - completed;

            let mut priority_counts = HashMap::new();
            for todo in self.todos.values() {
                let priority_str = match todo.priority {
                    Priority::Low => "low",
                    Priority::Medium => "medium",
                    Priority::High => "high",
                    Priority::Critical => "critical",
                }.to_string();
                *priority_counts.entry(priority_str).or_insert(0) += 1;
            }

            // Convert HashMap to Vec for rkyv compatibility
            let by_priority: Vec<(String, u32)> = priority_counts.into_iter().collect();

            TodoReaction::Statistics { total, completed, pending, by_priority }
        }

        /// Filter todos based on criteria
        fn filter_todos(&self, completed: Option<bool>, priority: Option<Priority>) -> Vec<Todo> {
            self.todos.values()
                .filter(|todo| {
                    if let Some(comp) = completed {
                        if todo.completed != comp { return false; }
                    }
                    if let Some(ref prio) = priority {
                        if std::mem::discriminant(&todo.priority) != std::mem::discriminant(prio) {
                            return false;
                        }
                    }
                    true
                })
                .cloned()
                .collect()
        }

        /// Search todos by title or description
        fn search_todos(&self, query: &str) -> Vec<Todo> {
            let query_lower = query.to_lowercase();
            self.todos.values()
                .filter(|todo| {
                    todo.title.to_lowercase().contains(&query_lower) ||
                    todo.description.to_lowercase().contains(&query_lower)
                })
                .cloned()
                .collect()
        }
    }

    #[async_trait]
    impl crate::Actor for TodoList {
        type Action = TodoAction;
        type Reaction = TodoReaction;

        async fn act(&mut self, action: Self::Action, actors: &Actors, _memories: &Memories) -> TamtilResult<Self::Reaction> {
            match action {
                TodoAction::Start => {
                    tracing::info!("TodoList {} starting", self.id.as_str());
                    Ok(TodoReaction::Started)
                }

                TodoAction::Stop => {
                    tracing::info!("TodoList {} stopping", self.id.as_str());
                    // Children auto-stop when parent stops
                    Ok(TodoReaction::Stopped)
                }

                TodoAction::Create { title, description, priority } => {
                    let todo = Todo {
                        id: self.next_id,
                        title,
                        description,
                        completed: false,
                        priority,
                        created_at: std::time::SystemTime::now()
                            .duration_since(std::time::UNIX_EPOCH)
                            .unwrap_or_default()
                            .as_secs(),
                    };

                    self.todos.insert(self.next_id, todo.clone());
                    self.next_id += 1;

                    tracing::info!("Created todo: {} - {}", todo.id, todo.title);

                    // TODO: Notify analytics if available
                    // Note: Inter-actor communication will be implemented in next iteration

                    Ok(TodoReaction::Created { todo })
                }

                TodoAction::Complete { id } => {
                    if let Some(todo) = self.todos.get_mut(&id) {
                        todo.completed = true;
                        tracing::info!("Completed todo: {} - {}", todo.id, todo.title);

                        // TODO: Notify analytics if available
                        // Note: Inter-actor communication will be implemented in next iteration

                        Ok(TodoReaction::Completed { id })
                    } else {
                        Ok(TodoReaction::NotFound { id })
                    }
                }

                TodoAction::Update { id, title, description, priority } => {
                    if let Some(todo) = self.todos.get_mut(&id) {
                        if let Some(new_title) = title {
                            todo.title = new_title;
                        }
                        if let Some(new_description) = description {
                            todo.description = new_description;
                        }
                        if let Some(new_priority) = priority {
                            todo.priority = new_priority;
                        }

                        tracing::info!("Updated todo: {} - {}", todo.id, todo.title);
                        Ok(TodoReaction::Updated { todo: todo.clone() })
                    } else {
                        Ok(TodoReaction::NotFound { id })
                    }
                }

                TodoAction::Delete { id } => {
                    if let Some(_) = self.todos.remove(&id) {
                        tracing::info!("Deleted todo: {}", id);

                        // TODO: Notify analytics if available
                        // Note: Inter-actor communication will be implemented in next iteration

                        Ok(TodoReaction::Deleted { id })
                    } else {
                        Ok(TodoReaction::NotFound { id })
                    }
                }

                TodoAction::List => {
                    let todos: Vec<Todo> = self.todos.values().cloned().collect();
                    Ok(TodoReaction::TodoList { todos })
                }

                TodoAction::Get { id } => {
                    if let Some(todo) = self.todos.get(&id) {
                        Ok(TodoReaction::Found { todo: todo.clone() })
                    } else {
                        Ok(TodoReaction::NotFound { id })
                    }
                }

                TodoAction::Filter { completed, priority } => {
                    let todos = self.filter_todos(completed, priority.clone());
                    let filter = format!("completed: {:?}, priority: {:?}", completed, priority);
                    Ok(TodoReaction::FilteredList { todos, filter })
                }

                TodoAction::Search { query } => {
                    let todos = self.search_todos(&query);
                    Ok(TodoReaction::SearchResults { todos, query })
                }

                TodoAction::Stats => {
                    Ok(self.generate_stats())
                }

                TodoAction::SpawnAnalytics => {
                    if self.analytics_id.is_some() {
                        return Ok(TodoReaction::Error {
                            message: "Analytics already spawned".to_string()
                        });
                    }

                    let analytics_id = actors.spawn("analytics").await?;
                    self.analytics_id = Some(analytics_id.clone());

                    tracing::info!("Spawned analytics actor: {}", analytics_id.as_str());
                    Ok(TodoReaction::AnalyticsSpawned { id: analytics_id.as_str().to_string() })
                }

                TodoAction::SpawnNotifier => {
                    if self.notifier_id.is_some() {
                        return Ok(TodoReaction::Error {
                            message: "Notifier already spawned".to_string()
                        });
                    }

                    let notifier_id = actors.spawn("notifier").await?;
                    self.notifier_id = Some(notifier_id.clone());

                    tracing::info!("Spawned notifier actor: {}", notifier_id.as_str());
                    Ok(TodoReaction::NotifierSpawned { id: notifier_id.as_str().to_string() })
                }
            }
        }
    }

    /// Analytics actions for tracking todo operations
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum AnalyticsAction {
        Start,
        Stop,
        TodoCreated { todo: Todo },
        TodoCompleted { id: u32 },
        TodoDeleted { id: u32 },
        GetReport,
    }

    /// Analytics reactions with usage statistics
    #[derive(Debug, Clone, Archive, Serialize, Deserialize, Default)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum AnalyticsReaction {
        #[default]
        Started,
        Stopped,
        Tracked,
        Report {
            total_created: u32,
            total_completed: u32,
            total_deleted: u32,
            completion_rate: f32,
        },
    }

    #[async_trait]
    impl crate::Reaction for AnalyticsReaction {
        async fn remember(&self) -> TamtilResult<Vec<MemoryOperation>> {
            let mut operations = Vec::new();

            match self {
                AnalyticsReaction::Report { total_created, total_completed, total_deleted, completion_rate } => {
                    // Store analytics report components in memory
                    operations.push(MemoryOperation::Create {
                        key: "analytics:total_created".to_string(),
                        value: MemoryValue::number(*total_created as f64),
                    });
                    operations.push(MemoryOperation::Create {
                        key: "analytics:total_completed".to_string(),
                        value: MemoryValue::number(*total_completed as f64),
                    });
                    operations.push(MemoryOperation::Create {
                        key: "analytics:total_deleted".to_string(),
                        value: MemoryValue::number(*total_deleted as f64),
                    });
                    operations.push(MemoryOperation::Create {
                        key: "analytics:completion_rate".to_string(),
                        value: MemoryValue::number(*completion_rate as f64),
                    });
                }
                _ => {
                    // Other reactions don't need memory operations
                }
            }

            Ok(operations)
        }
    }

    /// Analytics actor for tracking todo operations
    pub struct Analytics {
        id: ActorId,
        total_created: u32,
        total_completed: u32,
        total_deleted: u32,
    }

    impl Analytics {
        pub fn new(id: impl Into<ActorId>) -> Self {
            Self {
                id: id.into(),
                total_created: 0,
                total_completed: 0,
                total_deleted: 0,
            }
        }

        fn completion_rate(&self) -> f32 {
            if self.total_created == 0 {
                0.0
            } else {
                (self.total_completed as f32 / self.total_created as f32) * 100.0
            }
        }
    }

    #[async_trait]
    impl crate::Actor for Analytics {
        type Action = AnalyticsAction;
        type Reaction = AnalyticsReaction;

        async fn act(&mut self, action: Self::Action, _actors: &Actors, _memories: &Memories) -> TamtilResult<Self::Reaction> {
            match action {
                AnalyticsAction::Start => {
                    tracing::info!("Analytics {} starting", self.id.as_str());
                    Ok(AnalyticsReaction::Started)
                }

                AnalyticsAction::Stop => {
                    tracing::info!("Analytics {} stopping", self.id.as_str());
                    Ok(AnalyticsReaction::Stopped)
                }

                AnalyticsAction::TodoCreated { todo } => {
                    self.total_created += 1;
                    tracing::debug!("Analytics tracked todo creation: {} - {}", todo.id, todo.title);
                    Ok(AnalyticsReaction::Tracked)
                }

                AnalyticsAction::TodoCompleted { id } => {
                    self.total_completed += 1;
                    tracing::debug!("Analytics tracked todo completion: {}", id);
                    Ok(AnalyticsReaction::Tracked)
                }

                AnalyticsAction::TodoDeleted { id } => {
                    self.total_deleted += 1;
                    tracing::debug!("Analytics tracked todo deletion: {}", id);
                    Ok(AnalyticsReaction::Tracked)
                }

                AnalyticsAction::GetReport => {
                    let completion_rate = self.completion_rate();
                    Ok(AnalyticsReaction::Report {
                        total_created: self.total_created,
                        total_completed: self.total_completed,
                        total_deleted: self.total_deleted,
                        completion_rate,
                    })
                }
            }
        }
    }

    /// Notifier actions for sending notifications
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum NotifierAction {
        Start,
        Stop,
        SendNotification { message: String, priority: Priority },
        GetHistory,
    }

    /// Notifier reactions
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum NotifierReaction {
        Started,
        Stopped,
        Sent { message: String },
        History { notifications: Vec<String> },
    }

    #[async_trait]
    impl crate::Reaction for NotifierReaction {
        async fn remember(&self) -> TamtilResult<Vec<MemoryOperation>> {
            let mut operations = Vec::new();

            match self {
                NotifierReaction::Sent { message } => {
                    // Store the notification in memory
                    let timestamp = std::time::SystemTime::now()
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap_or_default()
                        .as_secs();

                    operations.push(MemoryOperation::Create {
                        key: format!("notification:{}", timestamp),
                        value: MemoryValue::string(message.clone()),
                    });
                }
                _ => {
                    // Other reactions don't need memory operations
                }
            }

            Ok(operations)
        }
    }

    /// Notifier actor for sending notifications
    pub struct Notifier {
        id: ActorId,
        history: Vec<String>,
    }

    impl Notifier {
        pub fn new(id: impl Into<ActorId>) -> Self {
            Self {
                id: id.into(),
                history: Vec::new(),
            }
        }
    }

    #[async_trait]
    impl crate::Actor for Notifier {
        type Action = NotifierAction;
        type Reaction = NotifierReaction;

        async fn act(&mut self, action: Self::Action, _actors: &Actors, _memories: &Memories) -> TamtilResult<Self::Reaction> {
            match action {
                NotifierAction::Start => {
                    tracing::info!("Notifier {} starting", self.id.as_str());
                    Ok(NotifierReaction::Started)
                }

                NotifierAction::Stop => {
                    tracing::info!("Notifier {} stopping", self.id.as_str());
                    Ok(NotifierReaction::Stopped)
                }

                NotifierAction::SendNotification { message, priority } => {
                    let notification = format!("[{:?}] {}", priority, message);
                    self.history.push(notification.clone());

                    tracing::info!("Notification sent: {}", notification);
                    Ok(NotifierReaction::Sent { message: notification })
                }

                NotifierAction::GetHistory => {
                    Ok(NotifierReaction::History { notifications: self.history.clone() })
                }
            }
        }
    }

    /// Run complete todo application demonstration
    pub async fn run() -> TamtilResult<()> {
        // Initialize tracing
        let _ = tracing_subscriber::fmt::try_init();

        println!("🚀 TAMTIL Todo Application - Complete Feature Showcase");
        println!("=====================================================");

        // Step 1: Setup the platform and hierarchical structure
        demonstrate_platform_setup().await?;

        // Step 2: Create and manage todos
        demonstrate_todo_management().await?;

        // Step 3: Show child actor spawning and communication
        demonstrate_child_actors().await?;

        // Step 4: Demonstrate filtering and search
        demonstrate_filtering_search().await?;

        // Step 5: Show analytics and statistics
        demonstrate_analytics().await?;

        // Step 6: Show rkyv serialization
        demonstrate_serialization().await?;

        // Step 7: Test hierarchical shutdown
        demonstrate_shutdown().await?;

        println!("\n🎉 Todo Application demonstration completed successfully!");
        println!("\n💡 TAMTIL features showcased:");
        println!("   ✅ Hierarchical addressing: todoapp.com/web/main/todos/analytics");
        println!("   ✅ Actor spawning: actors.spawn(name)");
        println!("   ✅ Inter-actor communication: actors.actor(id).act(action)");
        println!("   ✅ Child actor management and cleanup");
        println!("   ✅ rkyv zero-copy serialization");
        println!("   ✅ Action→reaction pattern");
        println!("   ✅ Real-world application patterns");
        println!("   ✅ Statistics and analytics");
        println!("   ✅ Filtering and search capabilities");

        Ok(())
    }

    /// Demonstrate distributed sharded interner with fault tolerance
    pub async fn run_distributed_demo() -> TamtilResult<()> {
        // Initialize tracing
        let _ = tracing_subscriber::fmt::try_init();

        println!("🌐 TAMTIL Distributed Sharded Interner Demo");
        println!("==========================================");
        println!("Demonstrating platform orchestration with context shards");
        println!("Each context forms its own fault-tolerant cluster\n");

        // Step 1: Setup distributed platform configuration
        let platform_id = ActorId::new("todoapp.distributed");

        // Create node configurations for a 3-node cluster
        let nodes = vec![
            NodeConfig {
                node_id: 1,
                address: "127.0.0.1".to_string(),
                port: 8001,
                platform_id: platform_id.clone(),
            },
            NodeConfig {
                node_id: 2,
                address: "127.0.0.1".to_string(),
                port: 8002,
                platform_id: platform_id.clone(),
            },
            NodeConfig {
                node_id: 3,
                address: "127.0.0.1".to_string(),
                port: 8003,
                platform_id: platform_id.clone(),
            },
        ];

        // Create context shards - each context gets its own fault-tolerant cluster
        let web_context_id = platform_id.child("web");
        let api_context_id = platform_id.child("api");

        let context_shards = vec![
            ContextShardConfig::new(
                web_context_id.clone(),
                1, // shard_id
                platform_id.clone(),
                nodes.clone(),
            ),
            ContextShardConfig::new(
                api_context_id.clone(),
                2, // shard_id
                platform_id.clone(),
                nodes.clone(),
            ),
        ];

        // Create platform shard configuration
        let platform_config = PlatformShardConfig::new(platform_id.clone(), context_shards.clone());

        // Create distributed platform (simulating node 1)
        let platform = Platform::with_sharding(
            platform_id.clone(),
            "./distributed_tamtil_data",
            platform_config,
            nodes[0].clone(), // This node is node 1
        );

        // Initialize context shards
        platform.init_context_shards(context_shards).await?;

        println!("✅ Distributed platform initialized with fault-tolerant context shards");
        println!("   Platform: {}", platform.platform_id().as_str());
        println!("   Context shards: web (shard 1), api (shard 2)");
        println!("   Each shard: 3-node cluster with OmniPaxos consensus\n");

        // Step 2: Demonstrate actor placement via consistent hashing
        let todo_actors = vec![
            web_context_id.child("main").child("todos").child("user1"),
            web_context_id.child("main").child("todos").child("user2"),
            api_context_id.child("v1").child("tasks").child("project1"),
            api_context_id.child("v1").child("tasks").child("project2"),
        ];

        println!("📍 Actor placement via platform orchestrator:");
        for actor_id in &todo_actors {
            // The platform would route these actors to appropriate shards
            println!("   Actor: {} -> routed to appropriate context shard", actor_id.as_str());
        }

        // Step 3: Create todo actors and demonstrate distributed state
        let todo1 = TodoList::new(todo_actors[0].clone());
        let todo2 = TodoList::new(todo_actors[1].clone());

        let handle1 = platform.spawn(todo_actors[0].clone(), todo1).await?;
        let handle2 = platform.spawn(todo_actors[1].clone(), todo2).await?;

        // Start the actors
        let _reaction1 = handle1.act(TodoAction::Start).await?;
        let _reaction2 = handle2.act(TodoAction::Start).await?;

        println!("\n🎯 Actors spawned and distributed across context shards");
        println!("   Each actor's state is replicated via OmniPaxos consensus");
        println!("   Fault tolerance: can survive node failures in each shard\n");

        // Step 4: Demonstrate fault-tolerant state operations
        let _reaction = handle1.act(TodoAction::Create {
            title: "Distributed Todo".to_string(),
            description: "This todo is replicated across the cluster".to_string(),
            priority: Priority::High,
        }).await?;

        println!("📝 Todo added via distributed consensus");
        println!("   State replicated across all nodes in the web context shard");
        println!("   Zero-copy access via rkyv + symbol-based storage\n");

        // Cleanup
        let _ = handle1.act(TodoAction::Stop).await;
        let _ = handle2.act(TodoAction::Stop).await;

        println!("✅ Distributed sharded interner demo completed!");
        println!("   Key achievements:");
        println!("   • Platform orchestrates multiple context shards");
        println!("   • Each context = fault-tolerant cluster with OmniPaxos");
        println!("   • Consistent hashing for actor placement");
        println!("   • Zero-copy rkyv serialization");
        println!("   • One thread per core friendly design");
        println!("   • Leverages existing platform/context/actor hierarchy");

        Ok(())
    }

    /// Demonstrate platform setup and hierarchical addressing
    async fn demonstrate_platform_setup() -> TamtilResult<()> {
        println!("\n📍 Step 1: Platform Setup & Hierarchical Addressing");
        println!("   todoapp.com");
        println!("   ├── web (context)");
        println!("   │   ├── main (context instance)");
        println!("   │   │   ├── todos (todo list actor)");
        println!("   │   │   │   ├── analytics (child actor)");
        println!("   │   │   │   └── notifier (child actor)");

        // Test hierarchical addressing
        let platform_id = ActorId::new("todoapp.com");
        let context_id = platform_id.child("web");
        let instance_id = context_id.child("main");
        let todos_id = instance_id.child("todos");
        let analytics_id = todos_id.child("analytics");
        let notifier_id = todos_id.child("notifier");

        println!("\n✅ Hierarchical IDs created:");
        println!("   Platform: {}", platform_id.as_str());
        println!("   Context: {}", context_id.as_str());
        println!("   Instance: {}", instance_id.as_str());
        println!("   Todos: {}", todos_id.as_str());
        println!("   Analytics: {}", analytics_id.as_str());
        println!("   Notifier: {}", notifier_id.as_str());

        // Verify relationships
        assert!(analytics_id.child_of(&todos_id));
        assert!(notifier_id.child_of(&todos_id));
        assert_eq!(analytics_id.depth(), 5);

        println!("✅ Hierarchical relationships verified");
        Ok(())
    }

    /// Demonstrate todo management operations
    async fn demonstrate_todo_management() -> TamtilResult<()> {
        println!("\n📝 Step 2: Todo Management Operations");

        let platform = Platform::new();
        let todos_id = ActorId::new("todoapp.com/web/main/todos");
        let todo_list = TodoList::new(todos_id.clone());
        let handle = platform.spawn(todos_id, todo_list).await?;

        // Start the todo list
        let reaction = handle.act(TodoAction::Start).await?;
        println!("✅ Todo list started: {:?}", reaction);

        // Create some todos
        let reaction = handle.act(TodoAction::Create {
            title: "Learn TAMTIL".to_string(),
            description: "Master the actor system".to_string(),
            priority: Priority::High,
        }).await?;
        println!("✅ Created todo: {:?}", reaction);

        let reaction = handle.act(TodoAction::Create {
            title: "Build todo app".to_string(),
            description: "Showcase all features".to_string(),
            priority: Priority::Medium,
        }).await?;
        println!("✅ Created todo: {:?}", reaction);

        let reaction = handle.act(TodoAction::Create {
            title: "Write tests".to_string(),
            description: "Ensure quality".to_string(),
            priority: Priority::Critical,
        }).await?;
        println!("✅ Created todo: {:?}", reaction);

        // Complete a todo
        let reaction = handle.act(TodoAction::Complete { id: 1 }).await?;
        println!("✅ Completed todo: {:?}", reaction);

        // Update a todo
        let reaction = handle.act(TodoAction::Update {
            id: 2,
            title: Some("Build amazing todo app".to_string()),
            description: None,
            priority: Some(Priority::High),
        }).await?;
        println!("✅ Updated todo: {:?}", reaction);

        // List all todos
        let reaction = handle.act(TodoAction::List).await?;
        println!("✅ Listed todos: {:?}", reaction);

        Ok(())
    }

    /// Demonstrate child actor spawning and communication
    async fn demonstrate_child_actors() -> TamtilResult<()> {
        println!("\n🏗️  Step 3: Child Actor Spawning & Communication");

        let platform = Platform::new();
        let todos_id = ActorId::new("todoapp.com/web/main/todos");
        let todo_list = TodoList::new(todos_id.clone());
        let handle = platform.spawn(todos_id, todo_list).await?;

        // Start todo list
        let _ = handle.act(TodoAction::Start).await?;

        // Spawn analytics child actor
        let reaction = handle.act(TodoAction::SpawnAnalytics).await?;
        println!("✅ Spawned analytics: {:?}", reaction);

        // Spawn notifier child actor
        let reaction = handle.act(TodoAction::SpawnNotifier).await?;
        println!("✅ Spawned notifier: {:?}", reaction);

        // Create a todo (will notify analytics)
        let reaction = handle.act(TodoAction::Create {
            title: "Test analytics".to_string(),
            description: "Verify child communication".to_string(),
            priority: Priority::Low,
        }).await?;
        println!("✅ Created todo with analytics tracking: {:?}", reaction);

        // Complete todo (will notify analytics)
        let reaction = handle.act(TodoAction::Complete { id: 1 }).await?;
        println!("✅ Completed todo with analytics tracking: {:?}", reaction);

        println!("✅ Child actor communication demonstrated");
        Ok(())
    }

    /// Demonstrate filtering and search capabilities
    async fn demonstrate_filtering_search() -> TamtilResult<()> {
        println!("\n🔍 Step 4: Filtering & Search Capabilities");

        let platform = Platform::new();
        let todos_id = ActorId::new("todoapp.com/web/main/todos");
        let todo_list = TodoList::new(todos_id.clone());
        let handle = platform.spawn(todos_id, todo_list).await?;

        // Start and create test data
        let _ = handle.act(TodoAction::Start).await?;

        // Create diverse todos for filtering
        let _ = handle.act(TodoAction::Create {
            title: "High priority task".to_string(),
            description: "Important work".to_string(),
            priority: Priority::High,
        }).await?;

        let _ = handle.act(TodoAction::Create {
            title: "Low priority task".to_string(),
            description: "Can wait".to_string(),
            priority: Priority::Low,
        }).await?;

        let _ = handle.act(TodoAction::Create {
            title: "Critical bug fix".to_string(),
            description: "Fix production issue".to_string(),
            priority: Priority::Critical,
        }).await?;

        // Complete one todo
        let _ = handle.act(TodoAction::Complete { id: 1 }).await?;

        // Test filtering by completion status
        let reaction = handle.act(TodoAction::Filter {
            completed: Some(true),
            priority: None
        }).await?;
        println!("✅ Filtered completed todos: {:?}", reaction);

        // Test filtering by priority
        let reaction = handle.act(TodoAction::Filter {
            completed: None,
            priority: Some(Priority::Critical)
        }).await?;
        println!("✅ Filtered critical todos: {:?}", reaction);

        // Test search functionality
        let reaction = handle.act(TodoAction::Search {
            query: "bug".to_string()
        }).await?;
        println!("✅ Search results for 'bug': {:?}", reaction);

        let reaction = handle.act(TodoAction::Search {
            query: "priority".to_string()
        }).await?;
        println!("✅ Search results for 'priority': {:?}", reaction);

        println!("✅ Filtering and search capabilities demonstrated");
        Ok(())
    }

    /// Demonstrate analytics and statistics
    async fn demonstrate_analytics() -> TamtilResult<()> {
        println!("\n📊 Step 5: Analytics & Statistics");

        let platform = Platform::new();
        let todos_id = ActorId::new("todoapp.com/web/main/todos");
        let todo_list = TodoList::new(todos_id.clone());
        let handle = platform.spawn(todos_id, todo_list).await?;

        // Start and create test data
        let _ = handle.act(TodoAction::Start).await?;

        // Create multiple todos
        for i in 1..=5 {
            let _ = handle.act(TodoAction::Create {
                title: format!("Task {}", i),
                description: format!("Description for task {}", i),
                priority: match i % 4 {
                    0 => Priority::Critical,
                    1 => Priority::High,
                    2 => Priority::Medium,
                    _ => Priority::Low,
                },
            }).await?;
        }

        // Complete some todos
        let _ = handle.act(TodoAction::Complete { id: 1 }).await?;
        let _ = handle.act(TodoAction::Complete { id: 3 }).await?;

        // Delete one todo
        let _ = handle.act(TodoAction::Delete { id: 5 }).await?;

        // Get statistics
        let reaction = handle.act(TodoAction::Stats).await?;
        println!("✅ Todo statistics: {:?}", reaction);

        println!("✅ Analytics and statistics demonstrated");
        Ok(())
    }

    /// Demonstrate hierarchical shutdown
    async fn demonstrate_shutdown() -> TamtilResult<()> {
        println!("\n🛑 Step 6: Hierarchical Shutdown");

        let platform = Platform::new();
        let todos_id = ActorId::new("todoapp.com/web/main/todos");
        let todo_list = TodoList::new(todos_id.clone());
        let handle = platform.spawn(todos_id, todo_list).await?;

        // Start and spawn children
        let _ = handle.act(TodoAction::Start).await?;
        let _ = handle.act(TodoAction::SpawnAnalytics).await?;
        let _ = handle.act(TodoAction::SpawnNotifier).await?;

        println!("✅ Created parent with child actors");

        // Stop parent (children auto-stop)
        let reaction = handle.act(TodoAction::Stop).await?;
        println!("✅ Parent stopped (children auto-stopped): {:?}", reaction);

        println!("✅ Hierarchical shutdown demonstrated");
        Ok(())
    }

    /// Demonstrate rkyv serialization with todo data
    async fn demonstrate_serialization() -> TamtilResult<()> {
        println!("\n🗜️  Bonus: rkyv Zero-Copy Serialization");

        // Test todo serialization
        let todo = Todo {
            id: 1,
            title: "Test serialization".to_string(),
            description: "Verify rkyv works".to_string(),
            completed: false,
            priority: Priority::Medium,
            created_at: 1234567890,
        };

        let bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&todo).unwrap();
        let archived = rkyv::access::<rkyv::Archived<Todo>, rkyv::rancor::Error>(&bytes).unwrap();

        println!("✅ Todo serialization: {} bytes", bytes.len());
        println!("   Original: {:?}", todo);
        println!("   Archived: {:?}", archived);

        // Test action serialization
        let action = TodoAction::Create {
            title: "Serialized todo".to_string(),
            description: "From rkyv".to_string(),
            priority: Priority::High,
        };

        let bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&action).unwrap();
        let archived = rkyv::access::<rkyv::Archived<TodoAction>, rkyv::rancor::Error>(&bytes).unwrap();

        println!("✅ Action serialization: {} bytes", bytes.len());
        println!("   Original: {:?}", action);
        println!("   Archived: {:?}", archived);

        println!("✅ Zero-copy serialization demonstrated");
        Ok(())
    }
}

// ============================================================================
// CHAPTER 4: COMPREHENSIVE TESTS - Testing the Todo Application
// ============================================================================

#[cfg(test)]
mod tests {
    use super::*;

    /// Test the complete todo application demonstration
    #[tokio::test]
    async fn test_complete_todo_application() {
        // Run the complete todo application demo
        todoapp::run().await.expect("Todo application should run successfully");
    }

    /// Test distributed sharded interner functionality
    #[tokio::test]
    async fn test_distributed_sharded_interner() {
        let _ = tracing_subscriber::fmt::try_init();

        // Test platform shard configuration
        let platform_id = ActorId::new("test.distributed");

        let nodes = vec![
            NodeConfig {
                node_id: 1,
                address: "127.0.0.1".to_string(),
                port: 9001,
                platform_id: platform_id.clone(),
            },
            NodeConfig {
                node_id: 2,
                address: "127.0.0.1".to_string(),
                port: 9002,
                platform_id: platform_id.clone(),
            },
        ];

        let web_context_id = platform_id.child("web");
        let context_shards = vec![
            ContextShardConfig::new(
                web_context_id.clone(),
                1,
                platform_id.clone(),
                nodes.clone(),
            ),
        ];

        let platform_config = PlatformShardConfig::new(platform_id.clone(), context_shards.clone());

        // Test actor routing via consistent hashing
        let actor1 = web_context_id.child("main").child("todos").child("user1");
        let actor2 = web_context_id.child("main").child("todos").child("user2");

        let routed_shard1 = platform_config.route_actor(&actor1);
        let routed_shard2 = platform_config.route_actor(&actor2);

        assert!(routed_shard1.is_some(), "Actor should be routed to a shard");
        assert!(routed_shard2.is_some(), "Actor should be routed to a shard");

        println!("✅ Actor routing test passed");

        // Test distributed platform creation (single-node simulation)
        let platform = Platform::with_sharding(
            platform_id.clone(),
            "./test_distributed_data",
            platform_config,
            nodes[0].clone(),
        );

        // Initialize context shards
        platform.init_context_shards(context_shards).await.expect("Should initialize shards");

        println!("✅ Distributed platform initialization test passed");

        // Test actor spawning on distributed platform
        let todo_list = todoapp::TodoList::new(actor1.clone());
        let handle = platform.spawn(actor1.clone(), todo_list).await.expect("Should spawn actor");

        // Test basic operations
        let reaction = handle.act(todoapp::TodoAction::Start).await.expect("Should start");
        assert!(matches!(reaction, todoapp::TodoReaction::Started));

        let reaction = handle.act(todoapp::TodoAction::Create {
            title: "Distributed test todo".to_string(),
            description: "Testing distributed interner".to_string(),
            priority: todoapp::Priority::Medium,
        }).await.expect("Should create todo");

        assert!(matches!(reaction, todoapp::TodoReaction::Created { .. }));

        println!("✅ Distributed actor operations test passed");

        // Cleanup
        let _ = std::fs::remove_dir_all("./test_distributed_data");
    }

    /// Test hierarchical addressing with todo app structure
    #[tokio::test]
    async fn test_todo_addressing_system() {
        // Test todo app hierarchical addressing
        let platform_id = ActorId::new("todoapp.com");
        let context_id = platform_id.child("web");
        let instance_id = context_id.child("main");
        let todos_id = instance_id.child("todos");
        let analytics_id = todos_id.child("analytics");
        let notifier_id = todos_id.child("notifier");

        // Test relationships
        assert!(context_id.child_of(&platform_id));
        assert!(analytics_id.child_of(&todos_id));
        assert!(notifier_id.child_of(&todos_id));
        assert_eq!(analytics_id.depth(), 5);
        assert_eq!(notifier_id.depth(), 5);

        // Test parent relationships
        assert_eq!(analytics_id.parent().unwrap(), todos_id);
        assert_eq!(todos_id.parent().unwrap(), instance_id);

        println!("✅ Todo addressing system tests passed");
    }

    /// Test disk persistence functionality
    #[tokio::test]
    async fn test_disk_persistence() {
        let _ = tracing_subscriber::fmt::try_init();

        // Create platform with custom disk path for testing
        let platform = Platform::with_disk_path("./test_tamtil_data");
        let todos_id = ActorId::new("test-todos-disk");
        let todo_list = todoapp::TodoList::new(todos_id.clone());
        let handle = platform.spawn(todos_id.clone(), todo_list).await.unwrap();

        // Start the actor
        let _ = handle.act(todoapp::TodoAction::Start).await.unwrap();

        // Create a todo (this should persist to disk)
        let reaction = handle.act(todoapp::TodoAction::Create {
            title: "Persistent todo".to_string(),
            description: "This should be saved to disk".to_string(),
            priority: todoapp::Priority::High,
        }).await.unwrap();

        if let todoapp::TodoReaction::Created { todo } = reaction {
            println!("✅ Created todo: {} (ID: {})", todo.title, todo.id);

            // Give the actor time to persist the data
            tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

            // Verify the todo was persisted by checking disk directly
            let disk = Disk::new("./test_tamtil_data");
            let keys = disk.keys(&todos_id).await.unwrap();
            println!("✅ Disk keys for actor {}: {:?}", todos_id.as_str(), keys);

            // Check if we can retrieve the todo title
            if let Some(title_value) = disk.get(&todos_id, &format!("todo:{}:title", todo.id)).await.unwrap() {
                println!("✅ Retrieved todo title from disk: {}", title_value);
            }

            // Get storage stats
            let stats = disk.stats(&todos_id).await.unwrap();
            println!("✅ Disk stats - File size: {} bytes, Entries: {}, Near capacity: {}",
                    stats.file_size, stats.entry_count, stats.near_capacity);

            // With the new universal interner, we should have entries for this actor
            assert!(stats.entry_count > 0, "Should have persisted data to disk via universal interner");

            // Verify the universal interner file exists and has data
            let interner_path = std::path::Path::new("./test_tamtil_data/universal_interner.tamtil");
            assert!(interner_path.exists(), "Universal interner file should exist");

            let metadata = std::fs::metadata(interner_path).unwrap();
            assert!(metadata.len() > 64, "Universal interner should contain data beyond header");

            println!("✅ Universal interner file size: {} bytes", metadata.len());
        } else {
            panic!("Expected TodoReaction::Created");
        }

        // Clean up test data
        let _ = std::fs::remove_dir_all("./test_tamtil_data");

        println!("✅ Disk persistence tests passed");
    }

    /// Test todo list basic operations
    #[tokio::test]
    async fn test_todo_basic_operations() {
        let _ = tracing_subscriber::fmt::try_init();

        let platform = Platform::new();
        let todos_id = ActorId::new("test-todos");
        let todo_list = todoapp::TodoList::new(todos_id.clone());
        let handle = platform.spawn(todos_id, todo_list).await.unwrap();

        // Test start
        let reaction = handle.act(todoapp::TodoAction::Start).await.unwrap();
        assert!(matches!(reaction, todoapp::TodoReaction::Started));

        // Test create todo
        let reaction = handle.act(todoapp::TodoAction::Create {
            title: "Test todo".to_string(),
            description: "Test description".to_string(),
            priority: todoapp::Priority::Medium,
        }).await.unwrap();

        if let todoapp::TodoReaction::Created { todo } = reaction {
            assert_eq!(todo.id, 1);
            assert_eq!(todo.title, "Test todo");
            assert!(!todo.completed);
        } else {
            panic!("Expected Created reaction");
        }

        // Test get todo
        let reaction = handle.act(todoapp::TodoAction::Get { id: 1 }).await.unwrap();
        assert!(matches!(reaction, todoapp::TodoReaction::Found { .. }));

        // Test complete todo
        let reaction = handle.act(todoapp::TodoAction::Complete { id: 1 }).await.unwrap();
        assert!(matches!(reaction, todoapp::TodoReaction::Completed { id: 1 }));

        // Test list todos
        let reaction = handle.act(todoapp::TodoAction::List).await.unwrap();
        if let todoapp::TodoReaction::TodoList { todos } = reaction {
            assert_eq!(todos.len(), 1);
            assert!(todos[0].completed);
        } else {
            panic!("Expected TodoList reaction");
        }

        println!("✅ Todo basic operations tests passed");
    }

    /// Test todo filtering and search
    #[tokio::test]
    async fn test_todo_filtering_search() {
        let _ = tracing_subscriber::fmt::try_init();

        let platform = Platform::new();
        let todos_id = ActorId::new("test-todos-filter");
        let todo_list = todoapp::TodoList::new(todos_id.clone());
        let handle = platform.spawn(todos_id, todo_list).await.unwrap();

        let _ = handle.act(todoapp::TodoAction::Start).await.unwrap();

        // Create test todos
        let _ = handle.act(todoapp::TodoAction::Create {
            title: "High priority task".to_string(),
            description: "Important work".to_string(),
            priority: todoapp::Priority::High,
        }).await.unwrap();

        let _ = handle.act(todoapp::TodoAction::Create {
            title: "Low priority task".to_string(),
            description: "Can wait".to_string(),
            priority: todoapp::Priority::Low,
        }).await.unwrap();

        let _ = handle.act(todoapp::TodoAction::Create {
            title: "Bug fix".to_string(),
            description: "Fix critical bug".to_string(),
            priority: todoapp::Priority::Critical,
        }).await.unwrap();

        // Complete one todo
        let _ = handle.act(todoapp::TodoAction::Complete { id: 1 }).await.unwrap();

        // Test filtering by completion
        let reaction = handle.act(todoapp::TodoAction::Filter {
            completed: Some(true),
            priority: None,
        }).await.unwrap();

        if let todoapp::TodoReaction::FilteredList { todos, .. } = reaction {
            assert_eq!(todos.len(), 1);
            assert!(todos[0].completed);
        } else {
            panic!("Expected FilteredList reaction");
        }

        // Test filtering by priority
        let reaction = handle.act(todoapp::TodoAction::Filter {
            completed: None,
            priority: Some(todoapp::Priority::Critical),
        }).await.unwrap();

        if let todoapp::TodoReaction::FilteredList { todos, .. } = reaction {
            assert_eq!(todos.len(), 1);
            assert!(matches!(todos[0].priority, todoapp::Priority::Critical));
        } else {
            panic!("Expected FilteredList reaction");
        }

        // Test search
        let reaction = handle.act(todoapp::TodoAction::Search {
            query: "bug".to_string(),
        }).await.unwrap();

        if let todoapp::TodoReaction::SearchResults { todos, .. } = reaction {
            assert_eq!(todos.len(), 1);
            assert!(todos[0].title.contains("Bug"));
        } else {
            panic!("Expected SearchResults reaction");
        }

        println!("✅ Todo filtering and search tests passed");
    }

    /// Test child actor spawning and communication
    #[tokio::test]
    async fn test_todo_child_actors() {
        let _ = tracing_subscriber::fmt::try_init();

        let platform = Platform::new();
        let todos_id = ActorId::new("test-todos-children");
        let todo_list = todoapp::TodoList::new(todos_id.clone());
        let handle = platform.spawn(todos_id, todo_list).await.unwrap();

        let _ = handle.act(todoapp::TodoAction::Start).await.unwrap();

        // Test spawning analytics
        let reaction = handle.act(todoapp::TodoAction::SpawnAnalytics).await.unwrap();
        if let todoapp::TodoReaction::AnalyticsSpawned { id } = reaction {
            assert!(id.contains("analytics"));
        } else {
            panic!("Expected AnalyticsSpawned reaction");
        }

        // Test spawning notifier
        let reaction = handle.act(todoapp::TodoAction::SpawnNotifier).await.unwrap();
        if let todoapp::TodoReaction::NotifierSpawned { id } = reaction {
            assert!(id.contains("notifier"));
        } else {
            panic!("Expected NotifierSpawned reaction");
        }

        // Test that we can't spawn duplicates
        let reaction = handle.act(todoapp::TodoAction::SpawnAnalytics).await.unwrap();
        assert!(matches!(reaction, todoapp::TodoReaction::Error { .. }));

        println!("✅ Todo child actor tests passed");
    }

    /// Test todo statistics
    #[tokio::test]
    async fn test_todo_statistics() {
        let _ = tracing_subscriber::fmt::try_init();

        let platform = Platform::new();
        let todos_id = ActorId::new("test-todos-stats");
        let todo_list = todoapp::TodoList::new(todos_id.clone());
        let handle = platform.spawn(todos_id, todo_list).await.unwrap();

        let _ = handle.act(todoapp::TodoAction::Start).await.unwrap();

        // Create multiple todos with different priorities
        for i in 1..=4 {
            let priority = match i {
                1 => todoapp::Priority::Critical,
                2 => todoapp::Priority::High,
                3 => todoapp::Priority::Medium,
                _ => todoapp::Priority::Low,
            };

            let _ = handle.act(todoapp::TodoAction::Create {
                title: format!("Task {}", i),
                description: format!("Description {}", i),
                priority,
            }).await.unwrap();
        }

        // Complete some todos
        let _ = handle.act(todoapp::TodoAction::Complete { id: 1 }).await.unwrap();
        let _ = handle.act(todoapp::TodoAction::Complete { id: 2 }).await.unwrap();

        // Get statistics
        let reaction = handle.act(todoapp::TodoAction::Stats).await.unwrap();
        if let todoapp::TodoReaction::Statistics { total, completed, pending, by_priority } = reaction {
            assert_eq!(total, 4);
            assert_eq!(completed, 2);
            assert_eq!(pending, 2);
            assert_eq!(by_priority.len(), 4); // All priority levels represented
        } else {
            panic!("Expected Statistics reaction");
        }

        println!("✅ Todo statistics tests passed");
    }

    /// Test rkyv serialization with todo data structures
    #[tokio::test]
    async fn test_todo_rkyv_serialization() {
        // Test todo serialization
        let todo = todoapp::Todo {
            id: 1,
            title: "Test serialization".to_string(),
            description: "Verify rkyv works with todos".to_string(),
            completed: false,
            priority: todoapp::Priority::High,
            created_at: 1234567890,
        };

        let bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&todo).unwrap();
        let _archived = rkyv::access::<rkyv::Archived<todoapp::Todo>, rkyv::rancor::Error>(&bytes).unwrap();

        assert!(!bytes.is_empty());
        println!("✅ Todo serialization: {} bytes", bytes.len());

        // Test action serialization
        let action = todoapp::TodoAction::Create {
            title: "Serialized todo".to_string(),
            description: "From rkyv".to_string(),
            priority: todoapp::Priority::Medium,
        };

        let bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&action).unwrap();
        let _archived = rkyv::access::<rkyv::Archived<todoapp::TodoAction>, rkyv::rancor::Error>(&bytes).unwrap();

        assert!(!bytes.is_empty());
        println!("✅ TodoAction serialization: {} bytes", bytes.len());

        // Test reaction serialization
        let reaction = todoapp::TodoReaction::Created { todo };
        let bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&reaction).unwrap();
        let _archived = rkyv::access::<rkyv::Archived<todoapp::TodoReaction>, rkyv::rancor::Error>(&bytes).unwrap();

        assert!(!bytes.is_empty());
        println!("✅ TodoReaction serialization: {} bytes", bytes.len());

        println!("✅ Todo rkyv serialization tests passed");
    }

    /// Test analytics actor functionality
    #[tokio::test]
    async fn test_analytics_actor() {
        let _ = tracing_subscriber::fmt::try_init();

        let platform = Platform::new();
        let analytics_id = ActorId::new("test-analytics");
        let analytics = todoapp::Analytics::new(analytics_id.clone());
        let handle = platform.spawn(analytics_id, analytics).await.unwrap();

        // Start analytics
        let reaction = handle.act(todoapp::AnalyticsAction::Start).await.unwrap();
        assert!(matches!(reaction, todoapp::AnalyticsReaction::Started));

        // Track some events
        let todo = todoapp::Todo {
            id: 1,
            title: "Test".to_string(),
            description: "Test".to_string(),
            completed: false,
            priority: todoapp::Priority::Medium,
            created_at: 0,
        };

        let _ = handle.act(todoapp::AnalyticsAction::TodoCreated { todo }).await.unwrap();
        let _ = handle.act(todoapp::AnalyticsAction::TodoCompleted { id: 1 }).await.unwrap();
        let _ = handle.act(todoapp::AnalyticsAction::TodoDeleted { id: 1 }).await.unwrap();

        // Get report
        let reaction = handle.act(todoapp::AnalyticsAction::GetReport).await.unwrap();
        if let todoapp::AnalyticsReaction::Report { total_created, total_completed, total_deleted, completion_rate } = reaction {
            assert_eq!(total_created, 1);
            assert_eq!(total_completed, 1);
            assert_eq!(total_deleted, 1);
            assert_eq!(completion_rate, 100.0);
        } else {
            panic!("Expected Report reaction");
        }

        println!("✅ Analytics actor tests passed");
    }

    /// Test notifier actor functionality
    #[tokio::test]
    async fn test_notifier_actor() {
        let _ = tracing_subscriber::fmt::try_init();

        let platform = Platform::new();
        let notifier_id = ActorId::new("test-notifier");
        let notifier = todoapp::Notifier::new(notifier_id.clone());
        let handle = platform.spawn(notifier_id, notifier).await.unwrap();

        // Start notifier
        let reaction = handle.act(todoapp::NotifierAction::Start).await.unwrap();
        assert!(matches!(reaction, todoapp::NotifierReaction::Started));

        // Send notifications
        let reaction = handle.act(todoapp::NotifierAction::SendNotification {
            message: "Test notification".to_string(),
            priority: todoapp::Priority::High,
        }).await.unwrap();

        if let todoapp::NotifierReaction::Sent { message } = reaction {
            assert!(message.contains("Test notification"));
            assert!(message.contains("High"));
        } else {
            panic!("Expected Sent reaction");
        }

        // Get history
        let reaction = handle.act(todoapp::NotifierAction::GetHistory).await.unwrap();
        if let todoapp::NotifierReaction::History { notifications } = reaction {
            assert_eq!(notifications.len(), 1);
            assert!(notifications[0].contains("Test notification"));
        } else {
            panic!("Expected History reaction");
        }

        println!("✅ Notifier actor tests passed");
    }
}
