thread 'rustc' panicked at compiler/rustc_hir_analysis/src/hir_ty_lowering/lint.rs:210:61:
$ident: found ImplItem(ImplItem { ident: Action#0, owner_id: DefId(0:545 ~ tamtil[9a7c]::platform::{impl#17}::Action), generics: Generics { params: [], predicates: [], has_where_clause_predicates: false, where_clause_span: helpers/tamtil/src/lib.rs:347:29: 347:29 (#0), span: helpers/tamtil/src/lib.rs:347:20: 347:20 (#0) }, kind: Type(Ty { hir_id: HirId(DefId(0:545 ~ tamtil[9a7c]::platform::{impl#17}::Action).4), span: helpers/tamtil/src/lib.rs:347:23: 347:29 (#0), kind: TraitObject([PolyTraitRef { bound_generic_params: [], modifiers: TraitBoundModifiers { constness: Never, polarity: Positive }, trait_ref: TraitRef { path: Path { span: helpers/tamtil/src/lib.rs:347:23: 347:29 (#0), res: Def(Trait, DefId(0:241 ~ tamtil[9a7c]::Action)), segments: [PathSegment { ident: Action#0, hir_id: HirId(DefId(0:545 ~ tamtil[9a7c]::platform::{impl#17}::Action).1), res: Def(Trait, DefId(0:241 ~ tamtil[9a7c]::Action)), args: None, infer_args: false }] }, hir_ref_id: HirId(DefId(0:545 ~ tamtil[9a7c]::platform::{impl#17}::Action).2) }, span: helpers/tamtil/src/lib.rs:347:23: 347:29 (#0) }], TaggedRef { pointer: Lifetime { hir_id: HirId(DefId(0:545 ~ tamtil[9a7c]::platform::{impl#17}::Action).3), ident: #0, res: ImplicitObjectLifetimeDefault }, tag: None }) }), defaultness: Final, span: helpers/tamtil/src/lib.rs:347:9: 347:30 (#0), vis_span: helpers/tamtil/src/lib.rs:347:9: 347:9 (#0) })
stack backtrace:
   0:        0x11a0d1617 - std::backtrace::Backtrace::create::h0a0aef5d3db59bdf
   1:        0x11a0d1565 - std::backtrace::Backtrace::force_capture::hbfe37df980449ac5
   2:        0x117d50517 - std[de24b4482a9a14c4]::panicking::update_hook::<alloc[102d5df83fb541b7]::boxed::Box<rustc_driver_impl[19967e5afe0dea52]::install_ice_hook::{closure#1}>>::{closure#0}
   3:        0x11a0edc3e - std::panicking::rust_panic_with_hook::h84ea5aa159431bb1
   4:        0x11a0ed878 - std::panicking::begin_panic_handler::{{closure}}::hd7b7bb6ef9d01e25
   5:        0x11a0ead19 - std::sys::backtrace::__rust_end_short_backtrace::hfe3d5bda55c4a32f
   6:        0x11a0ed4b4 - _rust_begin_unwind
   7:        0x11d41814f - core::panicking::panic_fmt::h4d7cb1c09666b3ff
   8:        0x11d493279 - rustc_hir[310544195cc71049]::hir::expect_failed::<&rustc_hir[310544195cc71049]::hir::Node>
   9:        0x1181cd2c2 - <dyn rustc_hir_analysis[a301682d08502f9c]::hir_ty_lowering::HirTyLowerer>::prohibit_or_lint_bare_trait_object_ty
  10:        0x118245d07 - <dyn rustc_hir_analysis[a301682d08502f9c]::hir_ty_lowering::HirTyLowerer>::lower_ty
  11:        0x118197ee8 - rustc_hir_analysis[a301682d08502f9c]::collect::type_of::type_of
  12:        0x119695c36 - rustc_query_impl[10ab633b7f6006a]::plumbing::__rust_begin_short_backtrace::<rustc_query_impl[10ab633b7f6006a]::query_impl::type_of::dynamic_query::{closure#2}::{closure#0}, rustc_middle[f320b9dc61b9fb8a]::query::erase::Erased<[u8; 8usize]>>
  13:        0x119416422 - rustc_query_system[2eb4c95542264dfe]::query::plumbing::try_execute_query::<rustc_query_impl[10ab633b7f6006a]::DynamicConfig<rustc_query_system[2eb4c95542264dfe]::query::caches::DefIdCache<rustc_middle[f320b9dc61b9fb8a]::query::erase::Erased<[u8; 8usize]>>, false, false, false>, rustc_query_impl[10ab633b7f6006a]::plumbing::QueryCtxt, true>
  14:        0x11969fa04 - rustc_query_impl[10ab633b7f6006a]::query_impl::type_of::get_query_incr::__rust_end_short_backtrace
  15:        0x117ff46a0 - rustc_middle[f320b9dc61b9fb8a]::query::plumbing::query_get_at::<rustc_query_system[2eb4c95542264dfe]::query::caches::DefIdCache<rustc_middle[f320b9dc61b9fb8a]::query::erase::Erased<[u8; 8usize]>>>
  16:        0x1181f019b - rustc_hir_analysis[a301682d08502f9c]::check::wfcheck::check_item
  17:        0x11814bb52 - rustc_hir_analysis[a301682d08502f9c]::check::wfcheck::check_well_formed
  18:        0x1196927fa - rustc_query_impl[10ab633b7f6006a]::plumbing::__rust_begin_short_backtrace::<rustc_query_impl[10ab633b7f6006a]::query_impl::check_well_formed::dynamic_query::{closure#2}::{closure#0}, rustc_middle[f320b9dc61b9fb8a]::query::erase::Erased<[u8; 1usize]>>
  19:        0x1194ba124 - rustc_query_system[2eb4c95542264dfe]::query::plumbing::try_execute_query::<rustc_query_impl[10ab633b7f6006a]::DynamicConfig<rustc_data_structures[bb5392424d196f2]::vec_cache::VecCache<rustc_span[21dca90a62eaa499]::def_id::LocalDefId, rustc_middle[f320b9dc61b9fb8a]::query::erase::Erased<[u8; 1usize]>, rustc_query_system[2eb4c95542264dfe]::dep_graph::graph::DepNodeIndex>, false, false, false>, rustc_query_impl[10ab633b7f6006a]::plumbing::QueryCtxt, true>
  20:        0x1196fd00e - rustc_query_impl[10ab633b7f6006a]::query_impl::check_well_formed::get_query_incr::__rust_end_short_backtrace
  21:        0x1180444c8 - <rustc_middle[f320b9dc61b9fb8a]::hir::ModuleItems>::par_opaques::<rustc_hir_analysis[a301682d08502f9c]::check::wfcheck::check_mod_type_wf::{closure#4}>::{closure#0}
  22:        0x11815cd9b - rustc_hir_analysis[a301682d08502f9c]::check::wfcheck::check_mod_type_wf
  23:        0x1196927ea - rustc_query_impl[10ab633b7f6006a]::plumbing::__rust_begin_short_backtrace::<rustc_query_impl[10ab633b7f6006a]::query_impl::check_mod_type_wf::dynamic_query::{closure#2}::{closure#0}, rustc_middle[f320b9dc61b9fb8a]::query::erase::Erased<[u8; 1usize]>>
  24:        0x119469913 - rustc_query_system[2eb4c95542264dfe]::query::plumbing::try_execute_query::<rustc_query_impl[10ab633b7f6006a]::DynamicConfig<rustc_query_system[2eb4c95542264dfe]::query::caches::DefaultCache<rustc_span[21dca90a62eaa499]::def_id::LocalModDefId, rustc_middle[f320b9dc61b9fb8a]::query::erase::Erased<[u8; 1usize]>>, false, false, false>, rustc_query_impl[10ab633b7f6006a]::plumbing::QueryCtxt, true>
  25:        0x1196c6e1e - rustc_query_impl[10ab633b7f6006a]::query_impl::check_mod_type_wf::get_query_incr::__rust_end_short_backtrace
  26:        0x1181e16c0 - rustc_hir_analysis[a301682d08502f9c]::check_crate
  27:        0x1187818f8 - rustc_interface[550efeb77be5d2b5]::passes::run_required_analyses
  28:        0x118784603 - rustc_interface[550efeb77be5d2b5]::passes::analysis
  29:        0x119695c4a - rustc_query_impl[10ab633b7f6006a]::plumbing::__rust_begin_short_backtrace::<rustc_query_impl[10ab633b7f6006a]::query_impl::analysis::dynamic_query::{closure#2}::{closure#0}, rustc_middle[f320b9dc61b9fb8a]::query::erase::Erased<[u8; 0usize]>>
  30:        0x11941aa81 - rustc_query_system[2eb4c95542264dfe]::query::plumbing::try_execute_query::<rustc_query_impl[10ab633b7f6006a]::DynamicConfig<rustc_query_system[2eb4c95542264dfe]::query::caches::SingleCache<rustc_middle[f320b9dc61b9fb8a]::query::erase::Erased<[u8; 0usize]>>, false, false, false>, rustc_query_impl[10ab633b7f6006a]::plumbing::QueryCtxt, true>
  31:        0x1196a18a8 - rustc_query_impl[10ab633b7f6006a]::query_impl::analysis::get_query_incr::__rust_end_short_backtrace
  32:        0x117cfa008 - rustc_interface[550efeb77be5d2b5]::passes::create_and_enter_global_ctxt::<core[c01bd641c0d952f4]::option::Option<rustc_interface[550efeb77be5d2b5]::queries::Linker>, rustc_driver_impl[19967e5afe0dea52]::run_compiler::{closure#0}::{closure#2}>
  33:        0x117d4bb78 - rustc_interface[550efeb77be5d2b5]::interface::run_compiler::<(), rustc_driver_impl[19967e5afe0dea52]::run_compiler::{closure#0}>::{closure#1}
  34:        0x117d3b5dd - std[de24b4482a9a14c4]::sys::backtrace::__rust_begin_short_backtrace::<rustc_interface[550efeb77be5d2b5]::util::run_in_thread_with_globals<rustc_interface[550efeb77be5d2b5]::util::run_in_thread_pool_with_globals<rustc_interface[550efeb77be5d2b5]::interface::run_compiler<(), rustc_driver_impl[19967e5afe0dea52]::run_compiler::{closure#0}>::{closure#1}, ()>::{closure#0}, ()>::{closure#0}::{closure#0}, ()>
  35:        0x117d54f8d - <<std[de24b4482a9a14c4]::thread::Builder>::spawn_unchecked_<rustc_interface[550efeb77be5d2b5]::util::run_in_thread_with_globals<rustc_interface[550efeb77be5d2b5]::util::run_in_thread_pool_with_globals<rustc_interface[550efeb77be5d2b5]::interface::run_compiler<(), rustc_driver_impl[19967e5afe0dea52]::run_compiler::{closure#0}>::{closure#1}, ()>::{closure#0}, ()>::{closure#0}::{closure#0}, ()>::{closure#1} as core[c01bd641c0d952f4]::ops::function::FnOnce<()>>::call_once::{shim:vtable#0}
  36:        0x11a0f966b - std::sys::pal::unix::thread::Thread::new::thread_start::hb3ca1d0af25d1cd1
  37:     0x7ff8008ea1d3 - __pthread_start


rustc version: 1.87.0-nightly (efea9896f 2025-03-08)
platform: x86_64-apple-darwin

query stack during panic:
#0 [type_of] computing type of `platform::<impl at helpers/tamtil/src/lib.rs:346:5: 346:32>::Action`
#1 [check_well_formed] checking that `platform::<impl at helpers/tamtil/src/lib.rs:346:5: 346:32>` is well-formed
#2 [check_mod_type_wf] checking that types are well-formed in module `platform`
#3 [analysis] running analysis passes on this crate
end of query stack
