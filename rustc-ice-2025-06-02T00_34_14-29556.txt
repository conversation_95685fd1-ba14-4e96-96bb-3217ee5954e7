thread 'rustc' panicked at compiler/rustc_hir_analysis/src/hir_ty_lowering/lint.rs:210:61:
$ident: found ImplItem(ImplItem { ident: spawn#0, owner_id: DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn), generics: Generics { params: [GenericParam { hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).133), def_id: DefId(0:271 ~ tamtil[9a7c]::{impl#17}::spawn::T), name: Plain(T#0), span: helpers/tamtil/src/lib.rs:1481:24: 1481:25 (#0), pure_wrt_drop: false, kind: Type { default: None, synthetic: false }, colon_span: Some(helpers/tamtil/src/lib.rs:1481:25: 1481:26 (#0)), source: Generics }, GenericParam { hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).134), def_id: DefId(0:3701 ~ tamtil[9a7c]::{impl#17}::spawn::'_), name: Fresh, span: helpers/tamtil/src/lib.rs:1482:9: 1482:10 (#0), pure_wrt_drop: false, kind: Lifetime { kind: Elided(Ampersand) }, colon_span: None, source: Generics }, GenericParam { hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).139), def_id: DefId(0:272 ~ tamtil[9a7c]::{impl#17}::spawn::impl Into<ActorId>), name: Plain(impl Into<ActorId>#0), span: helpers/tamtil/src/lib.rs:1483:13: 1483:31 (#0), pure_wrt_drop: false, kind: Type { default: None, synthetic: true }, colon_span: None, source: Generics }], predicates: [WherePredicate { hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).130), span: helpers/tamtil/src/lib.rs:1481:25: 1481:42 (#0), kind: BoundPredicate(WhereBoundPredicate { origin: GenericParam, bound_generic_params: [], bounded_ty: Ty { hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).132), span: helpers/tamtil/src/lib.rs:1481:24: 1481:25 (#0), kind: Path(Resolved(None, Path { span: helpers/tamtil/src/lib.rs:1481:24: 1481:25 (#0), res: Def(TyParam, DefId(0:271 ~ tamtil[9a7c]::{impl#17}::spawn::T)), segments: [PathSegment { ident: T#0, hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).131), res: Def(TyParam, DefId(0:271 ~ tamtil[9a7c]::{impl#17}::spawn::T)), args: None, infer_args: true }] })) }, bounds: [Trait(PolyTraitRef { bound_generic_params: [], modifiers: TraitBoundModifiers { constness: Never, polarity: Positive }, trait_ref: TraitRef { path: Path { span: helpers/tamtil/src/lib.rs:1481:27: 1481:32 (#0), res: Def(Trait, DefId(0:926 ~ tamtil[9a7c]::Actor)), segments: [PathSegment { ident: Actor#0, hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).127), res: Def(Trait, DefId(0:926 ~ tamtil[9a7c]::Actor)), args: None, infer_args: false }] }, hir_ref_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).128) }, span: helpers/tamtil/src/lib.rs:1481:27: 1481:32 (#0) }), Outlives(Lifetime { hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).129), ident: 'static#0, res: Static })] }) }, WherePredicate { hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).144), span: helpers/tamtil/src/lib.rs:1483:18: 1483:31 (#0), kind: BoundPredicate(WhereBoundPredicate { origin: ImplTrait, bound_generic_params: [], bounded_ty: Ty { hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).146), span: helpers/tamtil/src/lib.rs:1483:13: 1483:31 (#0), kind: Path(Resolved(None, Path { span: helpers/tamtil/src/lib.rs:1483:13: 1483:31 (#0), res: Def(TyParam, DefId(0:272 ~ tamtil[9a7c]::{impl#17}::spawn::impl Into<ActorId>)), segments: [PathSegment { ident: impl Into<ActorId>#0, hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).145), res: Def(TyParam, DefId(0:272 ~ tamtil[9a7c]::{impl#17}::spawn::impl Into<ActorId>)), args: None, infer_args: true }] })) }, bounds: [Trait(PolyTraitRef { bound_generic_params: [], modifiers: TraitBoundModifiers { constness: Never, polarity: Positive }, trait_ref: TraitRef { path: Path { span: helpers/tamtil/src/lib.rs:1483:18: 1483:31 (#0), res: Def(Trait, DefId(2:3284 ~ core[c01b]::convert::Into)), segments: [PathSegment { ident: Into#0, hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).142), res: Def(Trait, DefId(2:3284 ~ core[c01b]::convert::Into)), args: Some(GenericArgs { args: [Type(Ty { hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).140), span: helpers/tamtil/src/lib.rs:1483:23: 1483:30 (#0), kind: Path(Resolved(None, Path { span: helpers/tamtil/src/lib.rs:1483:23: 1483:30 (#0), res: Def(Struct, DefId(0:923 ~ tamtil[9a7c]::ActorId)), segments: [PathSegment { ident: ActorId#0, hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).141), res: Def(Struct, DefId(0:923 ~ tamtil[9a7c]::ActorId)), args: None, infer_args: false }] })) })], constraints: [], parenthesized: No, span_ext: helpers/tamtil/src/lib.rs:1483:22: 1483:31 (#0) }), infer_args: false }] }, hir_ref_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).143) }, span: helpers/tamtil/src/lib.rs:1483:18: 1483:31 (#0) })] }) }], has_where_clause_predicates: false, where_clause_span: helpers/tamtil/src/lib.rs:1485:38: 1485:38 (#0), span: helpers/tamtil/src/lib.rs:1481:23: 1481:43 (#0) }, kind: Fn(FnSig { header: FnHeader { safety: Normal(Safe), constness: NotConst, asyncness: Async(helpers/tamtil/src/lib.rs:1481:9: 1481:14 (#0)), abi: Rust }, decl: FnDecl { inputs: [Ty { hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).138), span: helpers/tamtil/src/lib.rs:1482:9: 1482:14 (#0), kind: Ref(Lifetime { hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).135), ident: '_#0, res: Param(DefId(0:3701 ~ tamtil[9a7c]::{impl#17}::spawn::'_)) }, MutTy { ty: Ty { hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).137), span: helpers/tamtil/src/lib.rs:1482:10: 1482:14 (#0), kind: Path(Resolved(None, Path { span: helpers/tamtil/src/lib.rs:1482:10: 1482:14 (#0), res: SelfTyAlias { alias_to: DefId(0:266 ~ tamtil[9a7c]::{impl#17}), forbid_generic: false, is_trait_impl: false }, segments: [PathSegment { ident: Self#0, hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).136), res: SelfTyAlias { alias_to: DefId(0:266 ~ tamtil[9a7c]::{impl#17}), forbid_generic: false, is_trait_impl: false }, args: None, infer_args: true }] })) }, mutbl: Not }) }, Ty { hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).148), span: helpers/tamtil/src/lib.rs:1483:13: 1483:31 (#0), kind: Path(Resolved(None, Path { span: helpers/tamtil/src/lib.rs:1483:13: 1483:31 (#0), res: Def(TyParam, DefId(0:272 ~ tamtil[9a7c]::{impl#17}::spawn::impl Into<ActorId>)), segments: [PathSegment { ident: impl Into<ActorId>#0, hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).147), res: Def(TyParam, DefId(0:272 ~ tamtil[9a7c]::{impl#17}::spawn::impl Into<ActorId>)), args: None, infer_args: true }] })) }, Ty { hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).149), span: helpers/tamtil/src/lib.rs:1484:16: 1484:17 (#0), kind: Path(Resolved(None, Path { span: helpers/tamtil/src/lib.rs:1484:16: 1484:17 (#0), res: Def(TyParam, DefId(0:271 ~ tamtil[9a7c]::{impl#17}::spawn::T)), segments: [PathSegment { ident: T#0, hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).150), res: Def(TyParam, DefId(0:271 ~ tamtil[9a7c]::{impl#17}::spawn::T)), args: None, infer_args: false }] })) }], output: Return(Ty { hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).163), span: helpers/tamtil/src/lib.rs:1481:5: 1485:38 (#3535), kind: OpaqueDef(OpaqueTy { hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).151), def_id: DefId(0:273 ~ tamtil[9a7c]::{impl#17}::spawn::{opaque#0}), bounds: [Trait(PolyTraitRef { bound_generic_params: [], modifiers: TraitBoundModifiers { constness: Never, polarity: Positive }, trait_ref: TraitRef { path: Path { span: helpers/tamtil/src/lib.rs:1481:5: 1485:38 (#3535), res: Def(Trait, DefId(2:52153 ~ core[c01b]::future::future::Future)), segments: [PathSegment { ident: future_trait#3535, hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).161), res: Def(Trait, DefId(2:52153 ~ core[c01b]::future::future::Future)), args: Some(GenericArgs { args: [], constraints: [AssocItemConstraint { hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).160), ident: Output#0, gen_args: GenericArgs { args: [], constraints: [], parenthesized: No, span_ext: no-location (#0) }, kind: Equality { term: Ty(Ty { hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).152), span: helpers/tamtil/src/lib.rs:1485:10: 1485:38 (#0), kind: Path(Resolved(None, Path { span: helpers/tamtil/src/lib.rs:1485:10: 1485:38 (#0), res: Def(TyAlias, DefId(0:37 ~ tamtil[9a7c]::TamtilResult)), segments: [PathSegment { ident: TamtilResult#0, hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).159), res: Def(TyAlias, DefId(0:37 ~ tamtil[9a7c]::TamtilResult)), args: Some(GenericArgs { args: [Type(Ty { hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).158), span: helpers/tamtil/src/lib.rs:1485:23: 1485:37 (#0), kind: TraitObject([PolyTraitRef { bound_generic_params: [], modifiers: TraitBoundModifiers { constness: Never, polarity: Positive }, trait_ref: TraitRef { path: Path { span: helpers/tamtil/src/lib.rs:1485:23: 1485:37 (#0), res: Def(Trait, DefId(0:39 ~ tamtil[9a7c]::ActorHandle)), segments: [PathSegment { ident: ActorHandle#0, hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).155), res: Def(Trait, DefId(0:39 ~ tamtil[9a7c]::ActorHandle)), args: Some(GenericArgs { args: [Type(Ty { hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).153), span: helpers/tamtil/src/lib.rs:1485:35: 1485:36 (#0), kind: Path(Resolved(None, Path { span: helpers/tamtil/src/lib.rs:1485:35: 1485:36 (#0), res: Def(TyParam, DefId(0:271 ~ tamtil[9a7c]::{impl#17}::spawn::T)), segments: [PathSegment { ident: T#0, hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).154), res: Def(TyParam, DefId(0:271 ~ tamtil[9a7c]::{impl#17}::spawn::T)), args: None, infer_args: false }] })) })], constraints: [], parenthesized: No, span_ext: helpers/tamtil/src/lib.rs:1485:34: 1485:37 (#0) }), infer_args: false }] }, hir_ref_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).156) }, span: helpers/tamtil/src/lib.rs:1485:23: 1485:37 (#0) }], TaggedRef { pointer: Lifetime { hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).157), ident: #0, res: ImplicitObjectLifetimeDefault }, tag: None }) })], constraints: [], parenthesized: No, span_ext: helpers/tamtil/src/lib.rs:1485:22: 1485:38 (#0) }), infer_args: false }] })) }) }, span: helpers/tamtil/src/lib.rs:1481:5: 1485:38 (#3535) }], parenthesized: No, span_ext: no-location (#0) }), infer_args: false }] }, hir_ref_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).162) }, span: helpers/tamtil/src/lib.rs:1481:5: 1485:38 (#3535) })], origin: AsyncFn { parent: DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn), in_trait_or_impl: None }, span: helpers/tamtil/src/lib.rs:1481:5: 1485:38 (#3535) }) }), c_variadic: false, implicit_self: RefImm, lifetime_elision_allowed: true }, span: helpers/tamtil/src/lib.rs:1481:5: 1485:38 (#0) }, BodyId { hir_id: HirId(DefId(0:270 ~ tamtil[9a7c]::{impl#17}::spawn).126) }), defaultness: Final, span: helpers/tamtil/src/lib.rs:1481:5: 1500:6 (#0), vis_span: helpers/tamtil/src/lib.rs:1481:5: 1481:8 (#0) })
stack backtrace:
   0:        0x11c61b617 - std::backtrace::Backtrace::create::h0a0aef5d3db59bdf
   1:        0x11c61b565 - std::backtrace::Backtrace::force_capture::hbfe37df980449ac5
   2:        0x11a29a517 - std[de24b4482a9a14c4]::panicking::update_hook::<alloc[102d5df83fb541b7]::boxed::Box<rustc_driver_impl[19967e5afe0dea52]::install_ice_hook::{closure#1}>>::{closure#0}
   3:        0x11c637c3e - std::panicking::rust_panic_with_hook::h84ea5aa159431bb1
   4:        0x11c637878 - std::panicking::begin_panic_handler::{{closure}}::hd7b7bb6ef9d01e25
   5:        0x11c634d19 - std::sys::backtrace::__rust_end_short_backtrace::hfe3d5bda55c4a32f
   6:        0x11c6374b4 - _rust_begin_unwind
   7:        0x11f96214f - core::panicking::panic_fmt::h4d7cb1c09666b3ff
   8:        0x11f9dd279 - rustc_hir[310544195cc71049]::hir::expect_failed::<&rustc_hir[310544195cc71049]::hir::Node>
   9:        0x11a7172c2 - <dyn rustc_hir_analysis[a301682d08502f9c]::hir_ty_lowering::HirTyLowerer>::prohibit_or_lint_bare_trait_object_ty
  10:        0x11a78fd07 - <dyn rustc_hir_analysis[a301682d08502f9c]::hir_ty_lowering::HirTyLowerer>::lower_ty
  11:        0x11a77bc59 - <dyn rustc_hir_analysis[a301682d08502f9c]::hir_ty_lowering::HirTyLowerer>::lower_generic_args_of_path::{closure#0}
  12:        0x11a718562 - <dyn rustc_hir_analysis[a301682d08502f9c]::hir_ty_lowering::HirTyLowerer>::lower_path_segment
  13:        0x11a7903ce - <dyn rustc_hir_analysis[a301682d08502f9c]::hir_ty_lowering::HirTyLowerer>::lower_ty
  14:        0x11a76fa55 - <dyn rustc_hir_analysis[a301682d08502f9c]::hir_ty_lowering::HirTyLowerer>::lower_assoc_item_constraint
  15:        0x11a77e065 - <dyn rustc_hir_analysis[a301682d08502f9c]::hir_ty_lowering::HirTyLowerer>::lower_poly_trait_ref
  16:        0x11a76ad8e - <dyn rustc_hir_analysis[a301682d08502f9c]::hir_ty_lowering::HirTyLowerer>::lower_bounds::<&[rustc_hir[310544195cc71049]::hir::GenericBound]>
  17:        0x11a749947 - rustc_hir_analysis[a301682d08502f9c]::collect::item_bounds::opaque_type_bounds
  18:        0x11a6cffbc - rustc_hir_analysis[a301682d08502f9c]::collect::item_bounds::explicit_item_bounds_with_filter
  19:        0x11a6cfd58 - rustc_hir_analysis[a301682d08502f9c]::collect::item_bounds::explicit_item_bounds
  20:        0x11bbdd7d5 - rustc_query_impl[10ab633b7f6006a]::plumbing::__rust_begin_short_backtrace::<rustc_query_impl[10ab633b7f6006a]::query_impl::explicit_item_bounds::dynamic_query::{closure#2}::{closure#0}, rustc_middle[f320b9dc61b9fb8a]::query::erase::Erased<[u8; 16usize]>>
  21:        0x11bb8f5ee - <rustc_query_impl[10ab633b7f6006a]::query_impl::explicit_item_bounds::dynamic_query::{closure#2} as core[c01bd641c0d952f4]::ops::function::FnOnce<(rustc_middle[f320b9dc61b9fb8a]::ty::context::TyCtxt, rustc_span[21dca90a62eaa499]::def_id::DefId)>>::call_once
  22:        0x11b94c2fe - rustc_query_system[2eb4c95542264dfe]::query::plumbing::try_execute_query::<rustc_query_impl[10ab633b7f6006a]::DynamicConfig<rustc_query_system[2eb4c95542264dfe]::query::caches::DefIdCache<rustc_middle[f320b9dc61b9fb8a]::query::erase::Erased<[u8; 16usize]>>, false, false, false>, rustc_query_impl[10ab633b7f6006a]::plumbing::QueryCtxt, true>
  23:        0x11bbeee7e - rustc_query_impl[10ab633b7f6006a]::query_impl::explicit_item_bounds::get_query_incr::__rust_end_short_backtrace
  24:        0x11a6e9cbb - <rustc_hir_analysis[a301682d08502f9c]::collect::CollectItemTypesVisitor as rustc_hir[310544195cc71049]::intravisit::Visitor>::visit_opaque_ty
  25:        0x11a649b78 - <rustc_hir_analysis[a301682d08502f9c]::collect::CollectItemTypesVisitor as rustc_hir[310544195cc71049]::intravisit::Visitor>::visit_fn
  26:        0x11a6eb8e0 - <rustc_hir_analysis[a301682d08502f9c]::collect::CollectItemTypesVisitor as rustc_hir[310544195cc71049]::intravisit::Visitor>::visit_impl_item
  27:        0x11a695bd8 - rustc_hir_analysis[a301682d08502f9c]::check::wfcheck::check_well_formed
  28:        0x11bbdc7fa - rustc_query_impl[10ab633b7f6006a]::plumbing::__rust_begin_short_backtrace::<rustc_query_impl[10ab633b7f6006a]::query_impl::check_well_formed::dynamic_query::{closure#2}::{closure#0}, rustc_middle[f320b9dc61b9fb8a]::query::erase::Erased<[u8; 1usize]>>
  29:        0x11ba04124 - rustc_query_system[2eb4c95542264dfe]::query::plumbing::try_execute_query::<rustc_query_impl[10ab633b7f6006a]::DynamicConfig<rustc_data_structures[bb5392424d196f2]::vec_cache::VecCache<rustc_span[21dca90a62eaa499]::def_id::LocalDefId, rustc_middle[f320b9dc61b9fb8a]::query::erase::Erased<[u8; 1usize]>, rustc_query_system[2eb4c95542264dfe]::dep_graph::graph::DepNodeIndex>, false, false, false>, rustc_query_impl[10ab633b7f6006a]::plumbing::QueryCtxt, true>
  30:        0x11bc4700e - rustc_query_impl[10ab633b7f6006a]::query_impl::check_well_formed::get_query_incr::__rust_end_short_backtrace
  31:        0x11a58e4c8 - <rustc_middle[f320b9dc61b9fb8a]::hir::ModuleItems>::par_opaques::<rustc_hir_analysis[a301682d08502f9c]::check::wfcheck::check_mod_type_wf::{closure#4}>::{closure#0}
  32:        0x11a6a707b - rustc_hir_analysis[a301682d08502f9c]::check::wfcheck::check_mod_type_wf
  33:        0x11bbdc7ea - rustc_query_impl[10ab633b7f6006a]::plumbing::__rust_begin_short_backtrace::<rustc_query_impl[10ab633b7f6006a]::query_impl::check_mod_type_wf::dynamic_query::{closure#2}::{closure#0}, rustc_middle[f320b9dc61b9fb8a]::query::erase::Erased<[u8; 1usize]>>
  34:        0x11b9b3913 - rustc_query_system[2eb4c95542264dfe]::query::plumbing::try_execute_query::<rustc_query_impl[10ab633b7f6006a]::DynamicConfig<rustc_query_system[2eb4c95542264dfe]::query::caches::DefaultCache<rustc_span[21dca90a62eaa499]::def_id::LocalModDefId, rustc_middle[f320b9dc61b9fb8a]::query::erase::Erased<[u8; 1usize]>>, false, false, false>, rustc_query_impl[10ab633b7f6006a]::plumbing::QueryCtxt, true>
  35:        0x11bc10e1e - rustc_query_impl[10ab633b7f6006a]::query_impl::check_mod_type_wf::get_query_incr::__rust_end_short_backtrace
  36:        0x11a72b6c0 - rustc_hir_analysis[a301682d08502f9c]::check_crate
  37:        0x11accb8f8 - rustc_interface[550efeb77be5d2b5]::passes::run_required_analyses
  38:        0x11acce603 - rustc_interface[550efeb77be5d2b5]::passes::analysis
  39:        0x11bbdfc4a - rustc_query_impl[10ab633b7f6006a]::plumbing::__rust_begin_short_backtrace::<rustc_query_impl[10ab633b7f6006a]::query_impl::analysis::dynamic_query::{closure#2}::{closure#0}, rustc_middle[f320b9dc61b9fb8a]::query::erase::Erased<[u8; 0usize]>>
  40:        0x11b964a81 - rustc_query_system[2eb4c95542264dfe]::query::plumbing::try_execute_query::<rustc_query_impl[10ab633b7f6006a]::DynamicConfig<rustc_query_system[2eb4c95542264dfe]::query::caches::SingleCache<rustc_middle[f320b9dc61b9fb8a]::query::erase::Erased<[u8; 0usize]>>, false, false, false>, rustc_query_impl[10ab633b7f6006a]::plumbing::QueryCtxt, true>
  41:        0x11bbeb8a8 - rustc_query_impl[10ab633b7f6006a]::query_impl::analysis::get_query_incr::__rust_end_short_backtrace
  42:        0x11a244008 - rustc_interface[550efeb77be5d2b5]::passes::create_and_enter_global_ctxt::<core[c01bd641c0d952f4]::option::Option<rustc_interface[550efeb77be5d2b5]::queries::Linker>, rustc_driver_impl[19967e5afe0dea52]::run_compiler::{closure#0}::{closure#2}>
  43:        0x11a295b78 - rustc_interface[550efeb77be5d2b5]::interface::run_compiler::<(), rustc_driver_impl[19967e5afe0dea52]::run_compiler::{closure#0}>::{closure#1}
  44:        0x11a2855dd - std[de24b4482a9a14c4]::sys::backtrace::__rust_begin_short_backtrace::<rustc_interface[550efeb77be5d2b5]::util::run_in_thread_with_globals<rustc_interface[550efeb77be5d2b5]::util::run_in_thread_pool_with_globals<rustc_interface[550efeb77be5d2b5]::interface::run_compiler<(), rustc_driver_impl[19967e5afe0dea52]::run_compiler::{closure#0}>::{closure#1}, ()>::{closure#0}, ()>::{closure#0}::{closure#0}, ()>
  45:        0x11a29ef8d - <<std[de24b4482a9a14c4]::thread::Builder>::spawn_unchecked_<rustc_interface[550efeb77be5d2b5]::util::run_in_thread_with_globals<rustc_interface[550efeb77be5d2b5]::util::run_in_thread_pool_with_globals<rustc_interface[550efeb77be5d2b5]::interface::run_compiler<(), rustc_driver_impl[19967e5afe0dea52]::run_compiler::{closure#0}>::{closure#1}, ()>::{closure#0}, ()>::{closure#0}::{closure#0}, ()>::{closure#1} as core[c01bd641c0d952f4]::ops::function::FnOnce<()>>::call_once::{shim:vtable#0}
  46:        0x11c64366b - std::sys::pal::unix::thread::Thread::new::thread_start::hb3ca1d0af25d1cd1
  47:     0x7ff8008ea1d3 - __pthread_start


rustc version: 1.87.0-nightly (efea9896f 2025-03-08)
platform: x86_64-apple-darwin

query stack during panic:
#0 [explicit_item_bounds] finding item bounds for `<impl at helpers/tamtil/src/lib.rs:1467:1: 1467:14>::spawn::{opaque#0}`
#1 [check_well_formed] checking that `<impl at helpers/tamtil/src/lib.rs:1467:1: 1467:14>::spawn` is well-formed
#2 [check_mod_type_wf] checking that types are well-formed in top-level module
#3 [analysis] running analysis passes on this crate
end of query stack
